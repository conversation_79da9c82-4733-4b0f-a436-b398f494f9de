-- Level system tables
CREATE TABLE IF NOT EXISTS levels (
    user_id BIGINT,
    server_id BIGINT,
    xp BIGINT DEFAULT 0,
    level INTEGER DEFAULT 0,
    total_xp BIGINT DEFAULT 0,
    last_message TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    PRIMARY KEY (user_id, server_id)
);

-- Index for faster leaderboard queries
CREATE INDEX IF NOT EXISTS levels_server_xp_idx ON levels(server_id, total_xp DESC);
CREATE INDEX IF NOT EXISTS levels_server_level_idx ON levels(server_id, level DESC);

-- Level rewards table (for future expansion)
CREATE TABLE IF NOT EXISTS level_rewards (
    server_id BIGINT,
    level INTEGER,
    role_id BIGINT,
    PRIMARY KEY (server_id, level)
);

-- Level settings table (for future expansion)
CREATE TABLE IF NOT EXISTS level_settings (
    server_id BIGINT PRIMARY KEY,
    enabled BOOLEAN DEFAULT TRUE,
    xp_per_message INTEGER DEFAULT 15,
    xp_cooldown INTEGER DEFAULT 60,
    level_up_message BOOLEAN DEFAULT TRUE,
    level_up_channel BIGINT DEFAULT NULL
);
