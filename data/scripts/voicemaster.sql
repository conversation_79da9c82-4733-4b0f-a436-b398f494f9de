-- VoiceMaster tables for temporary voice channel management

-- Server VoiceMaster settings
CREATE TABLE IF NOT EXISTS voicemaster_settings (
    guild_id BIGINT PRIMARY KEY,
    join_channel_id BIGINT,
    category_id BIGINT,
    default_name TEXT DEFAULT '{user}''s Channel',
    default_limit INTEGER DEFAULT 0,
    default_bitrate INTEGER DEFAULT 64000,
    default_region TEXT,
    interface_enabled BOOLEAN DEFAULT TRUE,
    auto_role BIGINT,
    join_role BIGINT,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    updated_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Temporary voice channels
CREATE TABLE IF NOT EXISTS voicemaster_channels (
    channel_id BIGINT PRIMARY KEY,
    owner_id BIGINT NOT NULL,
    guild_id BIGINT NOT NULL,
    is_locked BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    custom_name TEXT,
    custom_limit INTEGER,
    custom_bitrate INTEGER,
    custom_region TEXT,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    last_activity TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Channel permissions (for permit/reject functionality)
CREATE TABLE IF NOT EXISTS voicemaster_permissions (
    id BIGSERIAL PRIMARY KEY,
    channel_id BIGINT NOT NULL REFERENCES voicemaster_channels(channel_id) ON DELETE CASCADE,
    target_id BIGINT NOT NULL, -- User or role ID
    target_type TEXT NOT NULL, -- 'user' or 'role'
    permission_type TEXT NOT NULL, -- 'allow' or 'deny'
    permission TEXT NOT NULL, -- 'connect', 'speak', etc.
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Channel activity log (optional, for analytics)
CREATE TABLE IF NOT EXISTS voicemaster_activity (
    id BIGSERIAL PRIMARY KEY,
    channel_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    action TEXT NOT NULL, -- 'join', 'leave', 'lock', 'unlock', etc.
    timestamp TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS voicemaster_channels_guild_idx ON voicemaster_channels(guild_id);
CREATE INDEX IF NOT EXISTS voicemaster_channels_owner_idx ON voicemaster_channels(owner_id);
CREATE INDEX IF NOT EXISTS voicemaster_permissions_channel_idx ON voicemaster_permissions(channel_id);
CREATE INDEX IF NOT EXISTS voicemaster_activity_channel_idx ON voicemaster_activity(channel_id);
CREATE INDEX IF NOT EXISTS voicemaster_activity_timestamp_idx ON voicemaster_activity(timestamp);

-- Cleanup function to remove old activity logs (optional)
-- This can be called periodically to keep the activity table from growing too large
CREATE OR REPLACE FUNCTION cleanup_voicemaster_activity()
RETURNS void AS $$
BEGIN
    DELETE FROM voicemaster_activity 
    WHERE timestamp < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;
