import aiohttp
from urllib.parse import urlencode
import re
import base64
import time
import json
import logging

# Update these constants to match your config.py
SPOTIFY_CLIENT_ID = "1c97bf2fef6d4db0a4c1de091ae84e93"
SPOTIFY_CLIENT_SECRET = "ed852bd09e0b4e33985722683cf01be2"
SPOTIFY_REDIRECT_URI = "https://spotify-gein.onrender.com/callback"

logger = logging.getLogger(__name__)

# HTTP Client class
class HTTPClient:
    def __init__(self):
        self.session = None
        self.cxn = None  # Will be set by the bot
    
    def set_db_connection(self, cxn):
        """Set the database connection from the bot"""
        self.cxn = cxn
    
    async def get_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def get(self, url, headers=None, res_method="json"):
        session = await self.get_session()
        async with session.get(url, headers=headers) as resp:
            if resp.status != 200:
                text = await resp.text()
                logger.error(f"HTTP {resp.status} error for {url}: {text}")
                raise Exception(f"HTTP {resp.status}: {text}")
            
            if res_method == "json":
                try:
                    return await resp.json()
                except Exception as e:
                    text = await resp.text()
                    logger.error(f"Failed to parse JSON from {url}. Status: {resp.status}, Content-Type: {resp.headers.get('Content-Type')}, Body: {text[:200]}")
                    raise Exception(f"Invalid JSON response from Spotify API")
            return await resp.text()
    
    async def post(self, url, data=None, headers=None, res_method="json"):
        session = await self.get_session()
        async with session.post(url, data=data, headers=headers) as resp:
            if resp.status != 200:
                text = await resp.text()
                logger.error(f"HTTP {resp.status} error for {url}: {text}")
                raise Exception(f"HTTP {resp.status}: {text}")
            
            if res_method == "json":
                try:
                    return await resp.json()
                except Exception as e:
                    text = await resp.text()
                    logger.error(f"Failed to parse JSON from {url}. Status: {resp.status}, Content-Type: {resp.headers.get('Content-Type')}, Body: {text[:200]}")
                    raise Exception(f"Invalid JSON response from Spotify API")
            return await resp.text()
    
    async def put(self, url, headers=None, json=None, res_method="json"):
        session = await self.get_session()
        async with session.put(url, headers=headers, json=json) as resp:
            if res_method == "json":
                return await resp.json()
            return await resp.text()

# Initialize HTTP client
client = HTTPClient()


def url_to_uri(url):
    if "open.spotify.com" in url:
        sub_regex = r"(http[s]?:\/\/)?(open.spotify.com)\/"
        url = "spotify:" + re.sub(sub_regex, "", url)
        url = url.replace("/", ":")
        # remove session id (and other query stuff)
        uri = re.sub("\?.*", "", url)
        return uri


class CONSTANTS:
    WHITE_ICON = "https://cdn.discordapp.com/attachments/872338764276576266/927649624888602624/spotify_white.png"
    API_URL = "https://api.spotify.com/v1/"
    AUTH_URL = "https://accounts.spotify.com/authorize"
    TOKEN_URL = "https://accounts.spotify.com/api/token"
    SCOPES = [
        # Essential scopes for current functionality
        "user-top-read",                    # For top tracks/artists
        "user-read-recently-played",        # For recent tracks
        "playlist-read-private",            # For user playlists
        "user-read-private",                # For user profile
        # Additional scopes for now playing and playback
        "user-read-playback-state",         # For current playback state
        "user-read-currently-playing",      # For currently playing track
        "playlist-read-collaborative",      # For collaborative playlists
    ]


class Oauth:
    def __init__(self, scope=None):
        self.scope = " ".join(CONSTANTS.SCOPES)
        self.client_token = None

    @property
    def headers(self):
        """
        Return proper headers for all token requests
        """
        auth_header = base64.b64encode(
            (SPOTIFY_CLIENT_ID + ":" + SPOTIFY_CLIENT_SECRET).encode("ascii")
        )
        return {
            "Authorization": "Basic %s" % auth_header.decode("ascii"),
            "Content-Type": "application/x-www-form-urlencoded",
        }

    def get_auth_url(self, state):
        """
        Return an authorization url to get an access code
        """
        params = {
            "client_id": SPOTIFY_CLIENT_ID,
            "response_type": "code",
            "redirect_uri": SPOTIFY_REDIRECT_URI,
            "state": state,
            "scope": " ".join(CONSTANTS.SCOPES),
        }
        constructed = urlencode(params)
        return "%s?%s" % (CONSTANTS.AUTH_URL, constructed)

    def validate_token(self, token_info):
        """Checks a token is valid"""
        now = int(time.time())
        expires_at = token_info.get("expires_at", 0)
        is_valid = expires_at - now > 60
        logger.debug(f"Token validation: now={now}, expires_at={expires_at}, valid={is_valid}")
        return is_valid

    async def get_access_token(self, user_id, token_info):
        """Get a valid access token - don't refresh, let external OAuth handle it"""
        if not token_info:
            raise Exception("No token info available")
            
        # Check if token is expired
        if not self.validate_token(token_info):
            logger.warning(f"Token expired for user {user_id}, needs reconnection")
            raise Exception("Your Spotify connection has expired. Please reconnect your account.")
            
        return token_info["access_token"]

    async def refresh_access_token(self, user_id, refresh_token):
        try:
            if not refresh_token:
                raise ValueError("No refresh token provided")

            params = {
                "grant_type": "refresh_token", 
                "refresh_token": refresh_token,
                "client_id": SPOTIFY_CLIENT_ID,
                "client_secret": SPOTIFY_CLIENT_SECRET
            }
            logger.debug(f"Refreshing token for user {user_id}")

            headers = {"Content-Type": "application/x-www-form-urlencoded"}
            
            token_info = await client.post(
                CONSTANTS.TOKEN_URL, data=params, headers=headers, res_method="json"
            )

            if "error" in token_info:
                logger.error(f"Spotify API error refreshing token for user {user_id}: {token_info}")
                raise Exception(f"Spotify API error: {token_info.get('error_description', token_info.get('error'))}")

            if not token_info.get("refresh_token"):
                # Didn't get new refresh token.
                # Old one is still valid.
                token_info["refresh_token"] = refresh_token

            # Set expires_at
            token_info["expires_at"] = int(time.time()) + token_info.get("expires_in", 3600)

            query = """
                    INSERT INTO spotify_auth
                    VALUES ($1, $2)
                    ON CONFLICT (user_id)
                    DO UPDATE SET token_info = $2
                    WHERE spotify_auth.user_id = $1;
                    """
            await client.cxn.execute(query, user_id, json.dumps(token_info))
            logger.debug(f"Successfully refreshed and stored token for user {user_id}")

            return token_info
        except Exception as e:
            logger.error(f"Error refreshing access token for user {user_id}: {e}")
            # If refresh fails, the user needs to reconnect
            raise Exception("Your Spotify connection has expired. Please reconnect your account.")

    async def request_access_token(self, code):
        try:
            payload = {
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": SPOTIFY_REDIRECT_URI,
            }
            logger.debug(f"Requesting access token with code: {code[:10]}...")

            token_info = await client.post(
                CONSTANTS.TOKEN_URL, data=payload, headers=self.headers, res_method="json"
            )

            if "error" in token_info:
                logger.error(f"Spotify API error requesting token: {token_info}")
                raise Exception(f"Spotify API error: {token_info.get('error_description', token_info.get('error'))}")

            # Set expires_at
            token_info["expires_at"] = int(time.time()) + token_info.get("expires_in", 3600)
            logger.debug("Successfully obtained access token")

            return token_info
        except Exception as e:
            logger.error(f"Error requesting access token: {e}")
            raise

    async def get_client_token(self):
        """Gets the token or creates a new one if expired"""
        if self.client_token and self.validate_token(self.client_token):
            return self.client_token["access_token"]

        client_token = await self.request_client_token()

        client_token["expires_at"] = int(time.time()) + client_token["expires_in"]
        self.client_token = client_token
        return self.client_token["access_token"]

    async def request_client_token(self):
        """Obtains a token from Spotify and returns it"""
        payload = {"grant_type": "client_credentials"}
        return await client.post(
            CONSTANTS.TOKEN_URL, data=payload, headers=self.headers, res_method="json"
        )


oauth = Oauth()


class User:  # Spotify user w discord user_id
    def __init__(self, user_id, token_info):
        self.user_id = user_id
        self.token_info = token_info

    @classmethod
    async def load(cls, user_id):
        query = """
                SELECT token_info
                FROM spotify_auth
                WHERE user_id = $1;
                """
        token_info = await client.cxn.fetchval(query, int(user_id))

        if token_info:
            token_info = json.loads(token_info)
            return cls(user_id, token_info)

    async def auth(self):
        access_token = await oauth.get_access_token(self.user_id, self.token_info)

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        return headers

    async def get(self, url):
        try:
            headers = await self.auth()
            response = await client.get(url, headers=headers, res_method="json")
            logger.debug(f"Spotify API response for {url}: {response}")
            return response
        except Exception as e:
            logger.error(f"Spotify API error for user {self.user_id}: {e}")
            # Don't try to refresh - direct to external OAuth server
            error_str = str(e).lower()
            if any(x in error_str for x in ["json", "401", "unauthorized", "mimetype", "expired"]):
                logger.warning(f"Token invalid for user {self.user_id}, needs reconnection via OAuth server")
                raise Exception("Your Spotify connection has expired. Please reconnect your account.")
            raise e

    async def put(self, url, headers=None, json=None, res_method=None):
        headers = headers or await self.auth()
        return await client.put(url, headers=headers, json=json, res_method=res_method)

    async def get_profile(self):
        return await self.get(CONSTANTS.API_URL + "me")

    async def get_playback_state(self):
        return await self.get(CONSTANTS.API_URL + "me/player")

    async def get_currently_playing(self):
        return await self.get(CONSTANTS.API_URL + "me/player/currently-playing")

    async def get_devices(self):
        return await self.get(CONSTANTS.API_URL + "me/player/devices")

    async def transfer_playback(self, devices, play: bool = False):
        return await self.put(
            CONSTANTS.API_URL + "me/player", json={"device_ids": devices, "play": play}
        )

    async def get_recently_played(self, limit=50):
        params = {"limit": limit}
        query_params = urlencode(params)
        return await self.get(
            CONSTANTS.API_URL + "me/player/recently-played?" + query_params
        )

    async def get_top_tracks(self, limit=50, time_range="long_term"):
        params = {"limit": limit, "time_range": time_range}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/top/tracks?" + query_params)

    async def get_top_artists(self, limit=50, time_range="long_term"):
        params = {"limit": limit, "time_range": time_range}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/top/artists?" + query_params)

    async def get_top_albums(self, limit=50):
        params = {"limit": limit}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/albums?" + query_params)

    async def pause(self):
        return await self.put(CONSTANTS.API_URL + "me/player/pause")

    async def play(self, **kwargs):
        return await self.put(CONSTANTS.API_URL + "me/player/play", json=kwargs)

    async def skip_to_next(self):
        return await client.post(
            CONSTANTS.API_URL + "me/player/next",
            headers=await self.auth(),
            res_method=None,
        )

    async def skip_to_previous(self):
        return await client.post(
            CONSTANTS.API_URL + "me/player/previous",
            headers=await self.auth(),
            res_method=None,
        )

    async def seek(self, position):
        params = {"position_ms": position * 1000}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/seek?" + query_params)

    async def repeat(self, option):
        params = {"state": option}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/repeat?" + query_params)

    async def shuffle(self, option: bool):
        params = {"state": option}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/shuffle?" + query_params)

    async def volume(self, amount):
        params = {"volume_percent": amount}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/volume?" + query_params)

    async def enqueue(self, uri):
        params = {"uri": uri}
        query_params = urlencode(params)
        return await client.post(
            CONSTANTS.API_URL + "me/player/queue?" + query_params,
            headers=await self.auth(),
            res_method=None,
        )

    async def get_playlists(self, limit=50, offset=0):
        """Get a user's owned and followed playlists"""
        params = {"limit": limit, "offset": offset}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/playlists?" + query_params)

    async def get_playlist_tracks(self, playlist_id, limit=50, offset=0):
        """Get tracks from a specific playlist"""
        params = {"limit": limit, "offset": offset}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + f"playlists/{playlist_id}/tracks?" + query_params)

    async def get_track(self, track_id):
        """Get a specific track by ID"""
        return await self.get(CONSTANTS.API_URL + f"tracks/{track_id}")

    async def get_album_tracks(self, album_id, limit=50, offset=0):
        """Get tracks from a specific album"""
        params = {"limit": limit, "offset": offset}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + f"albums/{album_id}/tracks?" + query_params)


async def auth():
    access_token = await oauth.get_client_token()

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    return headers


async def _get(url):
    return await client.get(url, headers=await auth(), res_method="json")


async def get_playlist(uri):
    playlist_id = uri.split(":")[-1]
    # Strip any remaining query parameters
    if "?" in playlist_id:
        playlist_id = playlist_id.split("?")[0]
    return await _get(CONSTANTS.API_URL + f"playlists/{playlist_id}")


async def get_playlist_tracks(uri, limit=50, offset=0):
    """Get tracks from a playlist using public API"""
    playlist_id = uri.split(":")[-1]
    # Strip any remaining query parameters
    if "?" in playlist_id:
        playlist_id = playlist_id.split("?")[0]
    params = {"limit": limit, "offset": offset}
    query_params = urlencode(params)
    return await _get(CONSTANTS.API_URL + f"playlists/{playlist_id}/tracks?" + query_params)


async def get_track(uri):
    """Get a track using public API"""
    track_id = uri.split(":")[-1]
    # Strip any remaining query parameters
    if "?" in track_id:
        track_id = track_id.split("?")[0]
    return await _get(CONSTANTS.API_URL + f"tracks/{track_id}")


async def get_album_tracks(uri, limit=50, offset=0):
    """Get tracks from an album using public API"""
    album_id = uri.split(":")[-1]
    # Strip any remaining query parameters
    if "?" in album_id:
        album_id = album_id.split("?")[0]
    params = {"limit": limit, "offset": offset}
    query_params = urlencode(params)
    return await _get(CONSTANTS.API_URL + f"albums/{album_id}/tracks?" + query_params)


async def get_user_playlists(username, limit=50, offset=0):
    """Get a user's owned and followed playlists"""

    params = {"limit": limit, "offset": offset}
    query_params = urlencode(params)
    return await _get(CONSTANTS.API_URL + f"users/{username}/playlists?" + query_params)


async def get_currently_playing(self, token):
    url = "https://api.spotify.com/v1/me/player/currently-playing"
    headers = {"Authorization": f"Bearer {token}"}
    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as resp:
            if resp.status == 204:
                # No content, nothing is playing
                return None
            if resp.content_type != "application/json":
                # Not JSON, handle gracefully
                return None
            data = await resp.json()
            return data


import json

async def save_token(conn, user_id, token_info):
    """
    Store or update a user's Spotify token in the database using the given connection.
    """
    query = """
        INSERT INTO spotify_auth (user_id, token_info)
        VALUES ($1, $2)
        ON CONFLICT (user_id)
        DO UPDATE SET token_info = $2
        WHERE spotify_auth.user_id = $1;
    """
    await conn.execute(query, int(user_id), json.dumps(token_info))
