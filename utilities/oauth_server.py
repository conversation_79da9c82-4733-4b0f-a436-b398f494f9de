import asyncio
from aiohttp import web
import json
import logging
from utilities.spotify import oauth
from core import bot

logger = logging.getLogger(__name__)

async def handle_spotify_callback(request):
    """Handle the Spotify OAuth callback"""
    code = request.query.get('code')
    state = request.query.get('state')  # Discord user ID
    error = request.query.get('error')

    logger.info(f"Spotify callback received - code: {'present' if code else 'missing'}, state: {state}, error: {error}")

    if error:
        logger.error(f"Spotify OAuth error: {error}")
        error_html = f"""
        <html>
        <head><title>Authorization Denied</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #e22134;">❌ Authorization Denied</h1>
            <p>You denied access to your Spotify account.</p>
            <p>Please try again if you want to connect your Spotify account.</p>
        </body>
        </html>
        """
        return web.Response(text=error_html, content_type='text/html')

    if not code:
        logger.error("No authorization code provided in callback")
        return web.Response(text="No authorization code provided")

    if not state:
        logger.error("No state parameter provided in callback")
        return web.Response(text="No state parameter provided")

    try:
        # Exchange code for token
        logger.info(f"Exchanging code for token for user {state}")
        token_info = await oauth.request_access_token(code)

        # Store token info in database with Discord user ID
        user_id = int(state)
        query = """
                INSERT INTO spotify_auth
                VALUES ($1, $2)
                ON CONFLICT (user_id)
                DO UPDATE SET token_info = $2
                WHERE spotify_auth.user_id = $1;
                """
        await bot.cxn.execute(query, user_id, json.dumps(token_info))
        logger.info(f"Successfully stored Spotify token for user {user_id}")

        # Success page
        html = """
        <html>
        <head><title>Spotify Connected!</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #1db954;">🎵 Spotify Connected Successfully!</h1>
            <p>Your Spotify account has been linked to your Discord account.</p>
            <p>You can now close this window and return to Discord to use Spotify commands.</p>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')

    except Exception as e:
        logger.error(f"Error in Spotify callback for user {state}: {e}")
        error_html = f"""
        <html>
        <head><title>Connection Failed</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #e22134;">❌ Connection Failed</h1>
            <p>Error: {str(e)}</p>
            <p>Please try again or contact support.</p>
        </body>
        </html>
        """
        return web.Response(text=error_html, content_type='text/html')

async def start_oauth_server():
    """Start the OAuth callback server"""
    app = web.Application()
    app.add_routes([web.get('/callback', handle_spotify_callback)])
    
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '127.0.0.1', 8080)
    await site.start()
    
    return runner
