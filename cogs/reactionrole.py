import discord
from discord.ext import commands
from typing import Union
from collections import defaultdict

from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(ReactionRole(bot))


class ReactionRole(commands.Cog):
    """
    Manage reaction roles for your server
    """

    def __init__(self, bot):
        self.bot = bot
        self.reaction_role_cache = defaultdict(list)

    async def cog_load(self):
        """Called when the cog is loaded"""
        await self.load_reaction_roles()

    async def load_reaction_roles(self):
        """Load reaction roles from database"""
        if not self.bot.cxn:
            return
        try:
            query = """
                    SELECT * FROM reaction_roles;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                key = (record['server_id'], record['message_id'], record['channel_id'])
                self.reaction_role_cache[key].append({
                    'emoji': record['emoji'],
                    'role_id': record['role_id']
                })
        except Exception as e:
            print(f"Failed to load reaction roles: {e}")

    async def remove_reaction_roles_for_channel(self, channel: discord.TextChannel):
        """Remove all reaction roles for a channel"""
        query = """
                DELETE FROM reaction_roles 
                WHERE channel_id = $1 AND server_id = $2;
                """
        await self.bot.cxn.execute(query, channel.id, channel.guild.id)
        
        # Clear cache for this channel
        keys_to_remove = []
        for key in self.reaction_role_cache:
            if key[0] == channel.guild.id and key[2] == channel.id:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.reaction_role_cache[key]

    @decorators.group(
        invoke_without_command=True,
        aliases=['rer'],
        brief="Manage reaction roles for your server",
        implemented="2024-07-20 00:00:00.000000",
        updated="2024-07-20 00:00:00.000000",
    )
    @checks.guild_only()
    async def reactionrole(self, ctx):
        """
        Usage: {0}reactionrole
        Aliases: rr
        Permission: None
        Output:
            Shows help for reaction role commands.
        """
        await ctx.send_help(ctx.command)

    @reactionrole.command(
        name="add",
        brief="Add a reaction role to a message",
        usage="[message id] [channel] [emoji] [role]"
    )
    @checks.guild_only()
    @checks.has_perms(manage_roles=True)
    async def rr_add(self, ctx, message_id: int, channel: discord.TextChannel, emoji: Union[discord.Emoji, str], *, role: Union[discord.Role, str]):
        """
        Usage: {0}reactionrole add <message_id> <channel> <emoji> <role>
        Permission: Manage Roles
        Output:
            Adds a reaction role to the specified message.
            When users react with the emoji, they get the role.
        """
        try:
            message = await channel.fetch_message(message_id)
        except discord.NotFound:
            return await ctx.fail("Message not found.")

        if isinstance(role, str):
            role = discord.utils.get(ctx.guild.roles, name=role)
            if role is None:
                return await ctx.fail("Role not found.")

        # Check if reaction role already exists
        emoji_str = str(emoji)

        query = """
                SELECT * FROM reaction_roles
                WHERE server_id = $1 AND message_id = $2 AND channel_id = $3 AND emoji = $4;
                """
        check = await self.bot.cxn.fetchrow(query, ctx.guild.id, message.id, channel.id, emoji_str)
        if check:
            return await ctx.fail("A similar reaction role was already added.")

        try:
            await message.add_reaction(emoji)
            
            # Insert into database
            query = """
                    INSERT INTO reaction_roles
                    (server_id, message_id, channel_id, role_id, emoji)
                    VALUES ($1, $2, $3, $4, $5);
                    """
            await self.bot.cxn.execute(
                query, ctx.guild.id, message.id, channel.id,
                role.id, emoji_str
            )

            # Update cache
            key = (ctx.guild.id, message.id, channel.id)
            self.reaction_role_cache[key].append({
                'emoji': emoji_str,
                'role_id': role.id
            })
            
            await ctx.success(f"Added reaction role {emoji} for {role.mention}.")
        except discord.Forbidden:
            await ctx.fail("Unable to add reaction role for this role. Check bot permissions.")
        except Exception as e:
            await ctx.fail(f"Unable to add reaction role: {str(e)}")

    @reactionrole.command(
        name="remove",
        brief="Remove a reaction role from a message",
        usage="[message id] [channel] [emoji]"
    )
    @checks.guild_only()
    @checks.has_perms(manage_roles=True)
    async def rr_remove(self, ctx, message_id: int, channel: discord.TextChannel, emoji: Union[discord.Emoji, str]):
        """
        Usage: {0}reactionrole remove <message_id> <channel> <emoji>
        Permission: Manage Roles
        Output:
            Removes a reaction role from the specified message.
        """
        emoji_str = str(emoji)

        query = """
                SELECT * FROM reaction_roles
                WHERE server_id = $1 AND message_id = $2 AND channel_id = $3 AND emoji = $4;
                """
        check = await self.bot.cxn.fetchrow(query, ctx.guild.id, message_id, channel.id, emoji_str)
        if not check:
            return await ctx.fail("Couldn't find a reaction role with the given arguments.")

        query = """
                DELETE FROM reaction_roles
                WHERE server_id = $1 AND message_id = $2 AND channel_id = $3 AND emoji = $4;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, message_id, channel.id, emoji_str)

        # Update cache
        key = (ctx.guild.id, message_id, channel.id)
        if key in self.reaction_role_cache:
            self.reaction_role_cache[key] = [
                rr for rr in self.reaction_role_cache[key]
                if rr['emoji'] != emoji_str
            ]
            if not self.reaction_role_cache[key]:
                del self.reaction_role_cache[key]
        
        await ctx.success("Cleared reaction role.")

    @reactionrole.command(
        name="removeall",
        brief="Remove all reaction roles from the server",
        usage="<channel>"
    )
    @checks.guild_only()
    @checks.has_perms(manage_roles=True)
    async def rr_removeall(self, ctx, *, channel: discord.TextChannel = None):
        """
        Usage: {0}reactionrole removeall [channel]
        Permission: Manage Roles
        Output:
            Removes all reaction roles from the server
            or from a specific channel if provided.
        """
        query = """
                SELECT * FROM reaction_roles WHERE server_id = $1;
                """
        results = await self.bot.cxn.fetch(query, ctx.guild.id)
        if len(results) == 0:
            return await ctx.fail("No reaction roles found.")

        if channel:
            await self.remove_reaction_roles_for_channel(channel)
            return await ctx.success(f"Removed reaction roles for {channel.mention}.")
        
        # Remove all reaction roles for the guild
        query = """
                DELETE FROM reaction_roles WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id)
        
        # Clear cache for this guild
        keys_to_remove = [key for key in self.reaction_role_cache if key[0] == ctx.guild.id]
        for key in keys_to_remove:
            del self.reaction_role_cache[key]
        
        await ctx.success("Removed reaction roles for **all** channels.")

    @reactionrole.command(name="list", brief="List all the reaction roles from the server")
    @checks.guild_only()
    async def rr_list(self, ctx):
        """
        Usage: {0}reactionrole list
        Permission: None
        Output:
            Lists all reaction roles configured
            for this server.
        """
        query = """
                SELECT * FROM reaction_roles WHERE server_id = $1;
                """
        results = await self.bot.cxn.fetch(query, ctx.guild.id)
        if len(results) == 0:
            return await ctx.fail("No reaction roles found.")

        embeds = []
        entries = []
        
        for i, result in enumerate(results, 1):
            role = ctx.guild.get_role(result['role_id'])
            role_mention = role.mention if role else f"<@&{result['role_id']}>"
            
            try:
                channel = ctx.guild.get_channel(result['channel_id'])
                if channel:
                    message = await channel.fetch_message(result['message_id'])
                    jump_url = message.jump_url
                else:
                    jump_url = "https://none.none"
            except:
                jump_url = "https://none.none"
            
            entry = f"`{i}` {result['emoji']} - {role_mention} [message link]({jump_url})"
            entries.append(entry)
        
        # Split into pages of 10
        for i in range(0, len(entries), 10):
            page_entries = entries[i:i+10]
            embed = discord.Embed(
                title=f"Reaction Roles ({len(results)})",
                description="\n".join(page_entries),
                color=0x323339
            )
            embeds.append(embed)
        
        if len(embeds) == 1:
            await ctx.send(embed=embeds[0])
        else:
            await helpers.paginate(ctx, embeds)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction role assignment"""
        if not payload.guild_id or payload.user_id == self.bot.user.id:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        # Check if this is a reaction role
        key = (payload.guild_id, payload.message_id, payload.channel_id)
        if key not in self.reaction_role_cache:
            return

        emoji_str = str(payload.emoji)

        # Find matching reaction role
        role_id = None
        for rr in self.reaction_role_cache[key]:
            if rr['emoji'] == emoji_str:
                role_id = rr['role_id']
                break

        if not role_id:
            return

        role = guild.get_role(role_id)
        if not role:
            return

        try:
            if role not in member.roles:
                await member.add_roles(role, reason="Reaction role")
        except discord.Forbidden:
            pass  # No permission to assign role
        except Exception:
            pass  # Other errors are ignored

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction role removal"""
        if not payload.guild_id or payload.user_id == self.bot.user.id:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        # Check if this is a reaction role
        key = (payload.guild_id, payload.message_id, payload.channel_id)
        if key not in self.reaction_role_cache:
            return

        emoji_str = str(payload.emoji)

        # Find matching reaction role
        role_id = None
        for rr in self.reaction_role_cache[key]:
            if rr['emoji'] == emoji_str:
                role_id = rr['role_id']
                break

        if not role_id:
            return

        role = guild.get_role(role_id)
        if not role:
            return

        try:
            if role in member.roles:
                await member.remove_roles(role, reason="Reaction role removed")
        except discord.Forbidden:
            pass  # No permissions to remove role
