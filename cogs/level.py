import io
import math
import discord
from discord.ext import commands
from PIL import Image, ImageDraw, ImageFont
import aiohttp

from utilities import checks
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(Level(bot))


class Level(commands.Cog):
    """
    Level and XP system for servers.
    """

    def __init__(self, bot):
        self.bot = bot

    def help_custom(self):
        emoji = '📊'
        label = "Level"
        description = "Level and XP system commands"
        return emoji, label, description

    def calculate_level_from_messages(self, message_count):
        """Calculate level from message count using a simple formula"""
        if message_count < 0:
            return 0
        # Level = floor(sqrt(messages / 10))
        return int(math.sqrt(message_count / 10))

    def calculate_xp_from_messages(self, message_count):
        """Calculate XP from message count (messages * 10)"""
        return message_count * 10

    def calculate_messages_for_level(self, level):
        """Calculate messages required for a specific level"""
        return level * level * 10

    def calculate_messages_for_next_level(self, level):
        """Calculate messages required for the next level"""
        return self.calculate_messages_for_level(level + 1)

    async def get_user_message_count(self, user_id, server_id):
        """Get user's message count from the messages table"""
        query = """
            SELECT COUNT(*) as message_count
            FROM messages
            WHERE author_id = $1 AND server_id = $2
        """
        result = await self.bot.cxn.fetchval(query, user_id, server_id)
        return result or 0

    async def get_user_rank(self, user_id, server_id):
        """Get user's rank in the server based on message count"""
        query = """
            SELECT COUNT(*) + 1 as rank
            FROM (
                SELECT author_id, COUNT(*) as msg_count
                FROM messages
                WHERE server_id = $1
                GROUP BY author_id
            ) AS user_counts
            WHERE msg_count > (
                SELECT COUNT(*)
                FROM messages
                WHERE author_id = $2 AND server_id = $1
            )
        """
        result = await self.bot.cxn.fetchval(query, server_id, user_id)
        return result or 1

    async def get_leaderboard(self, server_id, limit=10):
        """Get server leaderboard based on message count"""
        query = """
            SELECT author_id, COUNT(*) as message_count
            FROM messages
            WHERE server_id = $1
            GROUP BY author_id
            ORDER BY message_count DESC
            LIMIT $2
        """
        return await self.bot.cxn.fetch(query, server_id, limit)



    @decorators.command(
        brief="Get the message leaderboard of the server",
        aliases=["levels", "lvlboard"]
    )
    @checks.guild_only()
    @checks.bot_has_perms(embed_links=True, attach_files=True)
    @checks.cooldown()
    async def leaderboard(self, ctx):
        """
        Usage: {0}leaderboard
        Aliases: {0}levels, {0}lvlboard
        Output: Shows the top 10 users by message count in the server
        """
        leaderboard_data = await self.get_leaderboard(ctx.guild.id, 10)

        if not leaderboard_data:
            embed = discord.Embed(
                title="📊 Server Leaderboard",
                description="No message data found for this server.",
                color=0x323339
            )
            return await ctx.send_or_reply(embed=embed)

        async with ctx.typing():
            # Generate leaderboard image
            buffer = await self.generate_leaderboard_image(leaderboard_data, ctx.guild)

            file = discord.File(fp=buffer, filename="leaderboard.png")
            await ctx.send_or_reply(file=file)

    async def generate_leaderboard_image(self, leaderboard_data, guild):
        """Generate a leaderboard image matching the provided design"""
        # Image dimensions
        width = 950
        height = 80 + (len(leaderboard_data) * 80)  # Header + entries

        # Create image with dark background
        img = Image.new('RGB', (width, height), color=(47, 49, 54))
        draw = ImageDraw.Draw(img)

        # Try to load fonts
        try:
            name_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 24)
            level_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 20)
            xp_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 16)
        except:
            # Fallback to default font
            name_font = ImageFont.load_default()
            level_font = ImageFont.load_default()
            xp_font = ImageFont.load_default()

        y_offset = 20

        for i, record in enumerate(leaderboard_data):
            user = guild.get_member(record['author_id']) or self.bot.get_user(record['author_id'])
            if not user:
                continue

            # Calculate level and XP from message count
            message_count = record['message_count']
            level = self.calculate_level_from_messages(message_count)
            xp = self.calculate_xp_from_messages(message_count)

            # Entry background (rounded rectangle effect)
            entry_y = y_offset + (i * 80)
            entry_rect = [20, entry_y, width - 20, entry_y + 70]

            # Draw rounded rectangle background
            draw.rounded_rectangle(entry_rect, radius=15, fill=(54, 57, 63))

            # Get user avatar
            avatar_img = None
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(str(user.display_avatar.url)) as resp:
                        if resp.status == 200:
                            avatar_data = await resp.read()
                            avatar_img = Image.open(io.BytesIO(avatar_data))
                            avatar_img = avatar_img.convert('RGBA')  # Ensure RGBA mode
                            avatar_img = avatar_img.resize((50, 50))

                            # Create circular mask
                            mask = Image.new('L', (50, 50), 0)
                            mask_draw = ImageDraw.Draw(mask)
                            mask_draw.ellipse((0, 0, 50, 50), fill=255)

                            # Create a new image with transparent background
                            circular_avatar = Image.new('RGBA', (50, 50), (0, 0, 0, 0))
                            circular_avatar.paste(avatar_img, (0, 0))
                            circular_avatar.putalpha(mask)
                            avatar_img = circular_avatar
            except:
                pass

            # Paste avatar
            if avatar_img:
                img.paste(avatar_img, (35, entry_y + 10), avatar_img)

            # Draw rank number with # symbol
            rank_text = f"#{i + 1}"
            rank_color = (255, 204, 77) if i == 0 else (255, 255, 255)  # Gold for #1
            draw.text((100, entry_y + 15), rank_text, fill=rank_color, font=name_font)

            # Draw username
            username = user.display_name
            if len(username) > 20:
                username = username[:17] + "..."
            draw.text((150, entry_y + 15), username, fill=(255, 255, 255), font=name_font)

            # Draw level (right side)
            level_text = f"Level {level}"
            level_bbox = draw.textbbox((0, 0), level_text, font=level_font)
            level_width = level_bbox[2] - level_bbox[0]
            draw.text((width - 50 - level_width, entry_y + 10), level_text, fill=(255, 255, 255), font=level_font)

            # Draw XP (right side, below level)
            if xp >= 1000:
                xp_text = f"XP {xp / 1000:.1f}K"
            else:
                xp_text = f"XP {xp}"

            xp_bbox = draw.textbbox((0, 0), xp_text, font=xp_font)
            xp_width = xp_bbox[2] - xp_bbox[0]
            draw.text((width - 50 - xp_width, entry_y + 40), xp_text, fill=(153, 170, 181), font=xp_font)

        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        return buffer

    @decorators.command(
        brief="Get the rank card of someone",
        aliases=["levelcard", "card"]
    )
    @checks.guild_only()
    @checks.bot_has_perms(embed_links=True, attach_files=True)
    @checks.cooldown()
    async def rank(self, ctx, user: converters.DiscordMember = None):
        """
        Usage: {0}rank [user]
        Aliases: {0}levelcard, {0}card
        Output: Shows a rank card for the specified user (or yourself)
        """
        user = user or ctx.author

        # Get user message count and calculate level/XP
        message_count = await self.get_user_message_count(user.id, ctx.guild.id)
        user_rank = await self.get_user_rank(user.id, ctx.guild.id)

        # Calculate level and XP from message count
        current_level = self.calculate_level_from_messages(message_count)
        total_xp = self.calculate_xp_from_messages(message_count)

        # Calculate progress to next level
        current_level_messages = self.calculate_messages_for_level(current_level)
        next_level_messages = self.calculate_messages_for_next_level(current_level)

        # Progress within current level (how many messages into this level)
        current_level_progress = message_count - current_level_messages
        # Total messages needed to complete this level
        messages_needed_for_next = next_level_messages - current_level_messages

        async with ctx.typing():
            # Generate rank card
            buffer = await self.generate_rank_card(
                user, user_rank, current_level, current_level_progress,
                messages_needed_for_next, total_xp, message_count
            )

            file = discord.File(fp=buffer, filename="rank.png")
            await ctx.send_or_reply(file=file)

    async def generate_rank_card(self, user, rank, level, current_progress, next_level_progress, total_xp, message_count):
        """Generate a rank card image using Pillow matching the provided design"""
        # Card dimensions
        width = 934
        height = 282

        # Create image with new background color
        img = Image.new('RGB', (width, height), color=(33, 39, 46))  # #21272e
        draw = ImageDraw.Draw(img)

        # Try to load fonts
        try:
            large_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 48)
            medium_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 32)
            small_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 24)
            tiny_font = ImageFont.truetype("./data/assets/KGCorneroftheSky.ttf", 18)
        except:
            # Fallback to default font
            large_font = ImageFont.load_default()
            medium_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
            tiny_font = ImageFont.load_default()

        # Get user avatar
        avatar_img = None
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(str(user.display_avatar.url)) as resp:
                    if resp.status == 200:
                        avatar_data = await resp.read()
                        avatar_img = Image.open(io.BytesIO(avatar_data))
                        avatar_img = avatar_img.resize((150, 150))

                        # Make avatar circular
                        mask = Image.new('L', (150, 150), 0)
                        mask_draw = ImageDraw.Draw(mask)
                        mask_draw.ellipse((0, 0, 150, 150), fill=255)

                        # Apply mask
                        avatar_img.putalpha(mask)
        except:
            pass

        # Paste avatar - shifted left
        if avatar_img:
            img.paste(avatar_img, (20, 66), avatar_img)  # Moved from 40 to 20

        # Draw username with glow effect - positioned higher to avoid collision and shifted left
        username = user.display_name
        if len(username) > 15:
            username = username[:12] + "..."

        # Add glow effect by drawing text multiple times with slight offsets
        glow_color = (100, 100, 100)
        for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
            draw.text((200 + offset[0], 110 + offset[1]), username, fill=glow_color, font=large_font)  # Moved from 240 to 200
        draw.text((200, 110), username, fill=(255, 255, 255), font=large_font)

        # Draw rank and level (top right) with glow - better aligned
        rank_text = f"RANK {rank}"
        level_text = f"LEVEL {level}"

        # Calculate text widths for better alignment
        rank_bbox = draw.textbbox((0, 0), rank_text, font=medium_font)
        rank_width = rank_bbox[2] - rank_bbox[0]

        level_bbox = draw.textbbox((0, 0), level_text, font=medium_font)
        level_width = level_bbox[2] - level_bbox[0]

        # Position rank text (right-aligned from a reference point)
        rank_x = width - 50 - level_width - 50 - rank_width  # Leave space for level text
        for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
            draw.text((rank_x + offset[0], 50 + offset[1]), rank_text, fill=glow_color, font=medium_font)
        draw.text((rank_x, 50), rank_text, fill=(255, 255, 255), font=medium_font)

        # Position level text (right-aligned)
        level_x = width - 50 - level_width
        for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
            draw.text((level_x + offset[0], 50 + offset[1]), level_text, fill=glow_color, font=medium_font)
        draw.text((level_x, 50), level_text, fill=(255, 255, 255), font=medium_font)

        # Draw progress (right-aligned with level text) with glow
        progress_text = f"{current_progress} / {next_level_progress}"
        progress_bbox = draw.textbbox((0, 0), progress_text, font=medium_font)
        progress_width = progress_bbox[2] - progress_bbox[0]
        progress_x = width - 50 - progress_width

        for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
            draw.text((progress_x + offset[0], 100 + offset[1]), progress_text, fill=glow_color, font=medium_font)
        draw.text((progress_x, 100), progress_text, fill=(255, 255, 255), font=medium_font)

        # Draw progress bar with more rounded corners - shifted left and positioned better
        bar_x = 200  # Moved from 240 to 200 to align with username
        bar_y = 185  # Moved down slightly to give username more space
        bar_width = width - 270  # Made wider by reducing the right margin
        bar_height = 25  # Slightly smaller height

        # Background bar with new color
        draw.rounded_rectangle([bar_x, bar_y, bar_x + bar_width, bar_y + bar_height],
                             radius=20, fill=(57, 59, 67))  # #393b43

        # Progress bar with new color and more rounded
        if next_level_progress > 0:
            progress_width = int((current_progress / next_level_progress) * bar_width)
            if progress_width > 0:
                draw.rounded_rectangle([bar_x, bar_y, bar_x + progress_width, bar_y + bar_height],
                                     radius=20, fill=(162, 167, 188))  # #a2a7bc

        # Draw only message icon and count (bottom section) - better aligned
        stats_y = 220  # Moved up to be closer to progress bar

        # Load message icon from assets - make it bigger
        try:
            msg_icon = Image.open("./data/assets/msg.png")
            msg_icon = msg_icon.resize((44, 44))  # Slightly smaller than before but still bigger than original
            img.paste(msg_icon, (200, stats_y - 2), msg_icon)  # Shifted left to align with other elements

            # Add glow to message count text - positioned right after icon
            msg_text = f"{message_count}"
            msg_x = 240  # Adjusted to be closer to the shifted icon
            for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
                draw.text((msg_x + offset[0], stats_y + 2 + offset[1]), msg_text, fill=glow_color, font=tiny_font)
            draw.text((msg_x, stats_y + 2), msg_text, fill=(255, 255, 255), font=tiny_font)
        except:
            # Fallback to Unicode emoji if image fails
            draw.text((200, stats_y), f"💬 {message_count}", fill=(255, 255, 255), font=tiny_font)

        # Total XP (right side) with glow - better aligned
        total_xp_text = f"TOTAL XP {total_xp:,}"
        total_xp_bbox = draw.textbbox((0, 0), total_xp_text, font=small_font)
        total_xp_width = total_xp_bbox[2] - total_xp_bbox[0]
        total_xp_x = width - 50 - total_xp_width

        # Add glow to total XP text
        for offset in [(1, 1), (-1, -1), (1, -1), (-1, 1)]:
            draw.text((total_xp_x + offset[0], stats_y + offset[1]), total_xp_text, fill=glow_color, font=small_font)
        draw.text((total_xp_x, stats_y), total_xp_text, fill=(255, 255, 255), font=small_font)

        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        return buffer



