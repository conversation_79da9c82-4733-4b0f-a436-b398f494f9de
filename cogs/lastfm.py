import typing
import discord
import logging
import aiohttp
import asyncio
import time
import hashlib

from discord.ext import commands, tasks
from urllib.parse import urlencode

from utilities import checks
from utilities import converters
from utilities import decorators
from utilities import pagination

logger = logging.getLogger(__name__)

# Import config for Last.fm API settings
try:
    from config import LASTFM
    LASTFM_API_KEY = LASTFM.api_key
    LASTFM_API_SECRET = LASTFM.api_secret
    LASTFM_API_URL = LASTFM.api_url
except ImportError:
    # Fallback values if config is not available
    LASTFM_API_KEY = "your_lastfm_api_key_here"
    LASTFM_API_SECRET = "your_lastfm_api_secret_here"
    LASTFM_API_URL = "https://ws.audioscrobbler.com/2.0/"

# Embed color matching user preference
EMBED_COLOR = 0x323339


class LastFMError(Exception):
    """Custom exception for Last.fm API errors"""
    pass


class LastFMPaginationView(discord.ui.View):
    """Simple pagination view for Last.fm results using buttons"""

    def __init__(self, ctx, entries, per_page=10):
        super().__init__(timeout=300)  # 5 minute timeout
        self.ctx = ctx
        self.entries = entries
        self.per_page = per_page
        self.current_page = 0
        self.max_pages = (len(entries) - 1) // per_page + 1

        # Create embed
        self.embed = discord.Embed(color=EMBED_COLOR)

        # Update button states
        self.update_buttons()

    def update_buttons(self):
        """Update button states based on current page"""
        self.first_page.disabled = self.current_page == 0
        self.prev_page.disabled = self.current_page == 0
        self.next_page.disabled = self.current_page >= self.max_pages - 1
        self.last_page.disabled = self.current_page >= self.max_pages - 1

    def get_page_content(self):
        """Get the content for the current page"""
        start = self.current_page * self.per_page
        end = start + self.per_page
        page_entries = self.entries[start:end]

        # Format entries with numbers
        formatted_entries = []
        for i, entry in enumerate(page_entries, start=start + 1):
            formatted_entries.append(f"{i}. {entry}")

        return "\n".join(formatted_entries)

    def update_embed(self):
        """Update the embed with current page content"""
        self.embed.description = self.get_page_content()
        if self.max_pages > 1:
            self.embed.set_footer(text=f"Page {self.current_page + 1}/{self.max_pages} ({len(self.entries)} entries)")
        else:
            self.embed.set_footer(text=f"{len(self.entries)} entries")

    async def send_initial_message(self):
        """Send the initial message with pagination"""
        self.update_embed()
        return await self.ctx.send(embed=self.embed, view=self)

    @discord.ui.button(label="<<", style=discord.ButtonStyle.gray)
    async def first_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = 0
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label="<", style=discord.ButtonStyle.gray)
    async def prev_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = max(0, self.current_page - 1)
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label=">", style=discord.ButtonStyle.gray)
    async def next_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = min(self.max_pages - 1, self.current_page + 1)
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label=">>", style=discord.ButtonStyle.gray)
    async def last_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = self.max_pages - 1
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label="🗑", style=discord.ButtonStyle.red)
    async def delete_message(self, interaction: discord.Interaction, _: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        await interaction.response.defer()
        await interaction.delete_original_response()
        self.stop()

    async def on_timeout(self):
        """Called when the view times out"""
        try:
            # Disable all buttons
            for item in self.children:
                item.disabled = True
            await self.message.edit(view=self)
        except:
            pass


class LastFMHTTPClient:
    """HTTP client for Last.fm API requests"""
    
    def __init__(self):
        self.session = None
        self.cxn = None  # Database connection
    
    def set_db_connection(self, cxn):
        """Set the database connection from the bot"""
        self.cxn = cxn
    
    async def get_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None
    
    def generate_signature(self, params):
        """Generate API signature for authenticated requests"""
        # Sort parameters and create signature string
        sorted_params = sorted(params.items())
        signature_string = ''.join([f"{k}{v}" for k, v in sorted_params])
        signature_string += LASTFM_API_SECRET
        
        # Generate MD5 hash
        return hashlib.md5(signature_string.encode('utf-8')).hexdigest()
    
    async def make_request(self, method, params=None, authenticated=False):
        """Make a request to the Last.fm API"""
        if params is None:
            params = {}
        
        params.update({
            'method': method,
            'api_key': LASTFM_API_KEY,
            'format': 'json'
        })
        
        if authenticated:
            params['api_sig'] = self.generate_signature(params)
        
        session = await self.get_session()
        
        try:
            async with session.get(LASTFM_API_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' in data:
                        raise LastFMError(f"Last.fm API Error: {data.get('message', 'Unknown error')}")
                    return data
                else:
                    raise LastFMError(f"HTTP Error: {response.status}")
        except aiohttp.ClientError as e:
            raise LastFMError(f"Request failed: {str(e)}")


# Global HTTP client instance
lastfm_client = LastFMHTTPClient()


async def setup(bot):
    await bot.add_cog(LastFM(bot))


class LastFM(commands.Cog):
    """
    Last.fm integration for music statistics and scrobbling.
    """

    def __init__(self, bot):
        self.bot = bot
        # Set the database connection for the Last.fm client
        lastfm_client.set_db_connection(bot.cxn)
        
        # Time period mappings
        self.time_periods = {
            "overall": "overall",
            "7day": "7day", 
            "1month": "1month",
            "3month": "3month",
            "6month": "6month",
            "12month": "12month"
        }

    async def cog_unload(self):
        """Clean up when cog is unloaded"""
        await lastfm_client.close_session()

    def truncate(self, string, max_chars=20):
        return (string[: max_chars - 3] + "...") if len(string) > max_chars else string

    def hyperlink(self, name, url, max_chars=20):
        return f"**[{self.truncate(name, max_chars)}]({url})**"

    async def get_lastfm_user(self, ctx, user):
        """Get Last.fm username for a Discord user"""
        try:
            query = "SELECT lastfm_username FROM lastfm_users WHERE user_id = $1"
            result = await self.bot.cxn.fetchval(query, user.id)
            
            if not result:
                if user == ctx.author:
                    await ctx.fail("You haven't connected your Last.fm account yet. Use `lastfm login` to get started.")
                else:
                    await ctx.fail(f"User **{user}** `{user.id}` hasn't connected their Last.fm account yet.")
                return None
            
            return result
        except Exception as e:
            logger.error(f"Error getting Last.fm user {user.id}: {e}")
            await ctx.fail("An error occurred while loading your Last.fm account. Please try again later.")
            return None

    def format_relative_time(self, timestamp):
        """Format timestamp as relative time"""
        if not timestamp:
            return "Unknown"
        
        try:
            # Handle both string and integer timestamps
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            
            now = int(time.time())
            diff = now - timestamp
            
            if diff < 60:
                return "just now"
            elif diff < 3600:
                minutes = diff // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            elif diff < 86400:
                hours = diff // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            else:
                days = diff // 86400
                return f"{days} day{'s' if days != 1 else ''} ago"
        except:
            return "Unknown"

    @decorators.group(
        name="lastfm",
        aliases=["fm", "lfm"],
        brief="Integrate your Last.fm account with hina and view your scrobble stats",
    )
    @checks.cooldown()
    async def _lastfm(self, ctx):
        if not ctx.invoked_subcommand:
            embed = discord.Embed(
                title="Last.fm Integration",
                description="Connect your Last.fm account to view detailed music statistics and scrobbles.",
                color=EMBED_COLOR
            )
            embed.add_field(
                name="Getting Started",
                value="Use `lastfm login` to connect your account",
                inline=False
            )
            embed.add_field(
                name="Popular Commands",
                value="`lastfm now` - Current playing\n`lastfm recent` - Recent tracks\n`lastfm topartists` - Top artists",
                inline=False
            )
            await ctx.send(embed=embed)

    @_lastfm.command(brief="Login and authenticate hina to use your Last.fm account")
    async def login(self, ctx, username: str):
        """
        Usage: {0}lastfm login <username>
        Output: Connect your Last.fm account to the bot
        """
        try:
            # Verify the username exists by getting user info
            data = await lastfm_client.make_request('user.getinfo', {'user': username})

            if not data.get('user'):
                await ctx.fail(f"Last.fm user '{username}' not found.")
                return

            # Store the username in database
            query = """
                    INSERT INTO lastfm_users (user_id, lastfm_username, connected_at)
                    VALUES ($1, $2, NOW() AT TIME ZONE 'UTC')
                    ON CONFLICT (user_id)
                    DO UPDATE SET lastfm_username = $2, connected_at = NOW() AT TIME ZONE 'UTC'
                    """
            await self.bot.cxn.execute(query, ctx.author.id, username)

            user_info = data['user']
            embed = discord.Embed(
                title="Last.fm Account Connected",
                description=f"Successfully connected to **{username}**",
                color=EMBED_COLOR
            )

            if user_info.get('image'):
                # Get the largest image
                images = user_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.add_field(name="Playcount", value=f"{int(user_info.get('playcount', 0)):,}", inline=True)
            embed.add_field(name="Registered", value=user_info.get('registered', {}).get('#text', 'Unknown'), inline=True)

            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error in lastfm login for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while connecting your Last.fm account.")

    @_lastfm.command(brief="Remove your Last.fm account from hina's system")
    async def logout(self, ctx):
        """
        Usage: {0}lastfm logout
        Output: Disconnect your Last.fm account from the bot
        """
        query = "DELETE FROM lastfm_users WHERE user_id = $1"
        result = await self.bot.cxn.execute(query, ctx.author.id)

        if result == "DELETE 0":
            await ctx.fail("You don't have a Last.fm account connected.")
        else:
            await ctx.success("Successfully disconnected your Last.fm account.")

    @_lastfm.command(brief="Show your currently playing song from Last.fm", aliases=["np"])
    async def now(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm now [user]
        Aliases: {0}nowplaying
        Output: Display currently playing or last played track
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 1
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail(f"No recent tracks found for {lastfm_username}.")
                return

            # Handle single track vs array
            track = tracks[0] if isinstance(tracks, list) else tracks

            is_playing = '@attr' in track and track['@attr'].get('nowplaying') == 'true'

            embed = discord.Embed(
                title=f"{'🎵 Now Playing' if is_playing else '🎵 Last Played'}",
                color=EMBED_COLOR
            )

            # Track info
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            embed.add_field(name="Track", value=f"**{track_name}**", inline=False)
            embed.add_field(name="Artist", value=artist_name, inline=True)

            # Album info
            album = track.get('album', {})
            if isinstance(album, dict):
                album_name = album.get('#text')
                if album_name:
                    embed.add_field(name="Album", value=album_name, inline=True)

            # Timestamp for last played
            if not is_playing and track.get('date'):
                timestamp = track['date'].get('uts')
                if timestamp:
                    embed.add_field(name="Played", value=self.format_relative_time(timestamp), inline=True)

            # Track image
            if track.get('image'):
                images = track['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting now playing for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your current track.")

    @_lastfm.command(brief="Show your currently playing song from Last.fm")
    async def nowplaying(self, ctx, *, user: converters.DiscordMember = None):
        """Alias for lastfm now command"""
        await self.now(ctx, user=user)

    @_lastfm.command(brief="Update your Last.fm library manually")
    async def update(self, ctx):
        """
        Usage: {0}lastfm update
        Output: Manually trigger a library update (placeholder for future scrobbling features)
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        # This is a placeholder - in a full implementation, this might trigger
        # a background task to update cached user data
        embed = discord.Embed(
            title="Library Update",
            description=f"Library update triggered for **{lastfm_username}**",
            color=EMBED_COLOR
        )
        await ctx.send(embed=embed)

    @_lastfm.command(brief="View your total Last.fm scrobbles")
    async def count(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm count [user]
        Output: Display total scrobble count
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('user.getinfo', {'user': lastfm_username})
            user_info = data.get('user', {})

            playcount = int(user_info.get('playcount', 0))

            embed = discord.Embed(
                title="Scrobble Count",
                color=EMBED_COLOR
            )
            embed.add_field(name="Total Scrobbles", value=f"{playcount:,}", inline=False)
            embed.set_footer(text=f"Last.fm • {user.display_name}")

            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting scrobble count for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your scrobble count.")

    @_lastfm.command(brief="View your Last.fm score and statistics")
    async def score(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm score [user]
        Output: Display comprehensive Last.fm statistics
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get user info
            user_data = await lastfm_client.make_request('user.getinfo', {'user': lastfm_username})
            user_info = user_data.get('user', {})

            # Get top artists for additional stats
            artists_data = await lastfm_client.make_request('user.gettopartists', {
                'user': lastfm_username,
                'period': 'overall',
                'limit': 1
            })

            embed = discord.Embed(
                title=f"Last.fm Statistics for {lastfm_username}",
                color=EMBED_COLOR
            )

            # Basic stats
            playcount = int(user_info.get('playcount', 0))
            embed.add_field(name="Total Scrobbles", value=f"{playcount:,}", inline=True)

            # Calculate average per day if registered date is available
            registered = user_info.get('registered', {})
            if registered and registered.get('unixtime'):
                reg_time = int(registered['unixtime'])
                days_since = (int(time.time()) - reg_time) // 86400
                if days_since > 0:
                    avg_per_day = playcount / days_since
                    embed.add_field(name="Avg/Day", value=f"{avg_per_day:.1f}", inline=True)

            # Top artist
            top_artists = artists_data.get('topartists', {}).get('artist', [])
            if top_artists:
                top_artist = top_artists[0] if isinstance(top_artists, list) else top_artists
                embed.add_field(name="Top Artist", value=top_artist.get('name', 'Unknown'), inline=True)

            # Profile image
            if user_info.get('image'):
                images = user_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting Last.fm score for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your Last.fm statistics.")

    @_lastfm.command(brief="View a user's Last.fm profile information")
    async def whois(self, ctx, username: str):
        """
        Usage: {0}lastfm whois <username>
        Output: Display Last.fm profile information for any user
        """
        try:
            data = await lastfm_client.make_request('user.getinfo', {'user': username})
            user_info = data.get('user', {})

            if not user_info:
                await ctx.fail(f"Last.fm user '{username}' not found.")
                return

            embed = discord.Embed(
                title=f"Last.fm Profile: {username}",
                color=EMBED_COLOR
            )

            # Basic info
            playcount = int(user_info.get('playcount', 0))
            embed.add_field(name="Total Scrobbles", value=f"{playcount:,}", inline=True)

            # Registration date
            registered = user_info.get('registered', {})
            if registered and registered.get('#text'):
                embed.add_field(name="Registered", value=registered['#text'], inline=True)

            # Real name if available
            realname = user_info.get('realname')
            if realname:
                embed.add_field(name="Real Name", value=realname, inline=True)

            # Country
            country = user_info.get('country')
            if country:
                embed.add_field(name="Country", value=country, inline=True)

            # Profile image
            if user_info.get('image'):
                images = user_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            # Profile URL
            url = user_info.get('url')
            if url:
                embed.add_field(name="Profile", value=f"[View on Last.fm]({url})", inline=False)

            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting Last.fm profile for {username}: {e}")
            await ctx.fail("An error occurred while fetching the Last.fm profile.")

    @_lastfm.command(brief="Show what song your n-th scrobble was")
    async def milestone(self, ctx, number: int, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm milestone <number> [user]
        Output: Show what track was your nth scrobble
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        if number <= 0:
            await ctx.fail("Milestone number must be positive.")
            return

        try:
            # Get user's total playcount first
            user_data = await lastfm_client.make_request('user.getinfo', {'user': lastfm_username})
            total_plays = int(user_data.get('user', {}).get('playcount', 0))

            if number > total_plays:
                await ctx.fail(f"You only have {total_plays:,} scrobbles. Cannot find milestone #{number:,}.")
                return

            # Calculate which page the milestone would be on
            # Last.fm returns tracks in reverse chronological order
            tracks_from_end = total_plays - number + 1
            page = (tracks_from_end - 1) // 200 + 1  # 200 tracks per page max

            # Get the tracks from that page
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 200,
                'page': page
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("Could not find milestone track.")
                return

            # Find the exact track
            track_index = (tracks_from_end - 1) % 200
            if track_index >= len(tracks):
                await ctx.fail("Could not find milestone track.")
                return

            track = tracks[track_index] if isinstance(tracks, list) else tracks

            embed = discord.Embed(
                title=f"Milestone #{number:,}",
                color=EMBED_COLOR
            )

            # Track info
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            embed.add_field(name="Track", value=f"**{track_name}**", inline=False)
            embed.add_field(name="Artist", value=artist_name, inline=True)

            # Album info
            album = track.get('album', {})
            if isinstance(album, dict):
                album_name = album.get('#text')
                if album_name:
                    embed.add_field(name="Album", value=album_name, inline=True)

            # Date played
            if track.get('date'):
                timestamp = track['date'].get('uts')
                if timestamp:
                    embed.add_field(name="Played", value=self.format_relative_time(timestamp), inline=True)

            # Track image
            if track.get('image'):
                images = track['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting milestone for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your milestone track.")

    @_lastfm.command(brief="See what song everyone is listening to in the server")
    async def playing(self, ctx):
        """
        Usage: {0}lastfm playing
        Output: Show what all connected users in the server are currently playing
        """
        # Get all Last.fm users in the server
        query = """
                SELECT user_id, lastfm_username
                FROM lastfm_users
                WHERE user_id = ANY($1::BIGINT[])
                """
        member_ids = [member.id for member in ctx.guild.members if not member.bot]
        results = await self.bot.cxn.fetch(query, member_ids)

        if not results:
            await ctx.fail("No Last.fm users found in this server.")
            return

        playing_users = []

        for record in results:
            try:
                user_id = record['user_id']
                username = record['lastfm_username']

                # Get current track
                data = await lastfm_client.make_request('user.getrecenttracks', {
                    'user': username,
                    'limit': 1
                })

                tracks = data.get('recenttracks', {}).get('track', [])
                if tracks:
                    track = tracks[0] if isinstance(tracks, list) else tracks

                    # Check if currently playing
                    if '@attr' in track and track['@attr'].get('nowplaying') == 'true':
                        member = ctx.guild.get_member(user_id)
                        if member:
                            track_name = track.get('name', 'Unknown Track')
                            artist_name = track.get('artist', {})
                            if isinstance(artist_name, dict):
                                artist_name = artist_name.get('#text', 'Unknown Artist')

                            playing_users.append(f"**{member.display_name}** - {track_name} by {artist_name}")

            except Exception as e:
                logger.error(f"Error getting playing track for user {user_id}: {e}")
                continue

        if not playing_users:
            embed = discord.Embed(
                title="Currently Playing",
                description="No one is currently listening to music.",
                color=EMBED_COLOR
            )
        else:
            embed = discord.Embed(
                title="Currently Playing",
                description="\n".join(playing_users[:10]),  # Limit to 10 users
                color=EMBED_COLOR
            )

            if len(playing_users) > 10:
                embed.set_footer(text=f"Showing 10 of {len(playing_users)} users")

        await ctx.send(embed=embed)

    @_lastfm.command(brief="Get a random artist recommendation from your library")
    async def recommendation(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm recommendation [user]
        Output: Get a random artist recommendation based on your listening history
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get top artists to recommend from
            data = await lastfm_client.make_request('user.gettopartists', {
                'user': lastfm_username,
                'period': 'overall',
                'limit': 50
            })

            artists = data.get('topartists', {}).get('artist', [])
            if not artists:
                await ctx.fail("No artists found in your library.")
                return

            # Pick a random artist
            import random
            artist = random.choice(artists)

            embed = discord.Embed(
                title="Artist Recommendation",
                color=EMBED_COLOR
            )

            artist_name = artist.get('name', 'Unknown Artist')
            playcount = int(artist.get('playcount', 0))

            embed.add_field(name="Artist", value=f"**{artist_name}**", inline=False)
            embed.add_field(name="Your Plays", value=f"{playcount:,}", inline=True)

            # Artist image
            if artist.get('image'):
                images = artist['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            # Artist URL
            url = artist.get('url')
            if url:
                embed.add_field(name="Last.fm", value=f"[View Artist]({url})", inline=True)

            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting recommendation for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while getting your recommendation.")

    @_lastfm.command(brief="Compare your music taste with another user")
    async def taste(self, ctx, other_user: converters.DiscordMember):
        """
        Usage: {0}lastfm taste <user>
        Output: Compare music taste compatibility with another user
        """
        user1_username = await self.get_lastfm_user(ctx, ctx.author)
        if not user1_username:
            return

        user2_username = await self.get_lastfm_user(ctx, other_user)
        if not user2_username:
            return

        try:
            # Get taste comparison
            data = await lastfm_client.make_request('tasteometer.compare', {
                'type1': 'user',
                'type2': 'user',
                'value1': user1_username,
                'value2': user2_username
            })

            comparison = data.get('comparison', {})
            result = comparison.get('result', {})

            score = float(result.get('score', 0))
            score_percentage = score * 100

            embed = discord.Embed(
                title="Music Taste Comparison",
                color=EMBED_COLOR
            )

            embed.add_field(
                name="Compatibility",
                value=f"{score_percentage:.1f}%",
                inline=False
            )

            # Common artists
            artists = result.get('artists', {}).get('artist', [])
            if artists:
                if not isinstance(artists, list):
                    artists = [artists]

                common_artists = [artist.get('name', 'Unknown') for artist in artists[:5]]
                embed.add_field(
                    name="Common Artists",
                    value="\n".join([f"• {artist}" for artist in common_artists]),
                    inline=False
                )

            embed.set_footer(text=f"{ctx.author.display_name} vs {other_user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error comparing taste between {user1_username} and {user2_username}: {e}")
            await ctx.fail("An error occurred while comparing music taste.")

    @_lastfm.command(brief="View your current listening streak")
    async def streak(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm streak [user]
        Output: Show current listening streak information
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get recent tracks to calculate streak
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 200
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            # Calculate streak (consecutive days with scrobbles)
            streak_days = 0
            last_day = None

            for track in tracks:
                if track.get('date') and track['date'].get('uts'):
                    track_time = int(track['date']['uts'])
                    track_day = track_time // 86400  # Convert to day number

                    if last_day is None:
                        last_day = track_day
                        streak_days = 1
                    elif track_day == last_day - 1:
                        last_day = track_day
                        streak_days += 1
                    elif track_day != last_day:
                        break

            embed = discord.Embed(
                title="Listening Streak",
                color=EMBED_COLOR
            )

            embed.add_field(
                name="Current Streak",
                value=f"{streak_days} day{'s' if streak_days != 1 else ''}",
                inline=False
            )

            # Show most recent track
            if tracks:
                recent_track = tracks[0] if isinstance(tracks, list) else tracks
                track_name = recent_track.get('name', 'Unknown Track')
                artist_name = recent_track.get('artist', {})
                if isinstance(artist_name, dict):
                    artist_name = artist_name.get('#text', 'Unknown Artist')

                embed.add_field(
                    name="Last Track",
                    value=f"{track_name} by {artist_name}",
                    inline=False
                )

            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting streak for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while calculating your streak.")

    @_lastfm.command(brief="View your recent tracks")
    async def recent(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm recent [user]
        Output: Display recently played tracks
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 50
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            # Format tracks for pagination
            entries = []
            for track in tracks[:50]:  # Limit to 50 tracks
                track_name = track.get('name', 'Unknown Track')
                artist_name = track.get('artist', {})
                if isinstance(artist_name, dict):
                    artist_name = artist_name.get('#text', 'Unknown Artist')

                # Check if currently playing
                if '@attr' in track and track['@attr'].get('nowplaying') == 'true':
                    entries.append(f"🎵 **{track_name}** by {artist_name} *(now playing)*")
                else:
                    # Add timestamp
                    timestamp = ""
                    if track.get('date') and track['date'].get('uts'):
                        timestamp = f" - {self.format_relative_time(track['date']['uts'])}"
                    entries.append(f"**{track_name}** by {artist_name}{timestamp}")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{user.display_name}'s Recent Tracks"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting recent tracks for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your recent tracks.")

    @_lastfm.command(brief="View recent tracks for a specific artist")
    async def recentfor(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm recentfor <artist>
        Output: Show recent tracks you've played by a specific artist
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            # Get all recent tracks and filter by artist
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 200
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            # Filter tracks by artist
            artist_tracks = []
            for track in tracks:
                track_artist = track.get('artist', {})
                if isinstance(track_artist, dict):
                    track_artist = track_artist.get('#text', '')

                if artist_name.lower() in track_artist.lower():
                    track_name = track.get('name', 'Unknown Track')

                    # Add timestamp
                    timestamp = ""
                    if track.get('date') and track['date'].get('uts'):
                        timestamp = f" - {self.format_relative_time(track['date']['uts'])}"
                    elif '@attr' in track and track['@attr'].get('nowplaying') == 'true':
                        timestamp = " - *now playing*"

                    artist_tracks.append(f"**{track_name}**{timestamp}")

            if not artist_tracks:
                await ctx.fail(f"No recent tracks found for artist '{artist_name}'.")
                return

            # Create pagination view
            view = LastFMPaginationView(ctx, artist_tracks, per_page=10)
            view.embed.title = f"Recent tracks by {artist_name}"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting recent tracks for artist {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching recent tracks for this artist.")

    @_lastfm.command(brief="View how many plays you have for an artist")
    async def plays(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm plays <artist>
        Output: Show your play count for a specific artist
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('artist.getinfo', {
                'artist': artist_name,
                'username': lastfm_username
            })

            artist_info = data.get('artist', {})
            if not artist_info:
                await ctx.fail(f"Artist '{artist_name}' not found.")
                return

            embed = discord.Embed(
                title="Artist Play Count",
                color=EMBED_COLOR
            )

            artist_name = artist_info.get('name', artist_name)
            user_playcount = int(artist_info.get('stats', {}).get('userplaycount', 0))

            embed.add_field(name="Artist", value=f"**{artist_name}**", inline=False)
            embed.add_field(name="Your Plays", value=f"{user_playcount:,}", inline=True)

            # Global playcount
            global_playcount = int(artist_info.get('stats', {}).get('playcount', 0))
            if global_playcount > 0:
                embed.add_field(name="Global Plays", value=f"{global_playcount:,}", inline=True)

            # Artist image
            if artist_info.get('image'):
                images = artist_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting plays for artist {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching artist play count.")

    @_lastfm.command(brief="Check your play count for a specific track")
    async def playstrack(self, ctx, artist_name: str, *, track_name: str):
        """
        Usage: {0}lastfm playstrack <artist> <track>
        Output: Show your play count for a specific track
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('track.getinfo', {
                'artist': artist_name,
                'track': track_name,
                'username': lastfm_username
            })

            track_info = data.get('track', {})
            if not track_info:
                await ctx.fail(f"Track '{track_name}' by '{artist_name}' not found.")
                return

            embed = discord.Embed(
                title="Track Play Count",
                color=EMBED_COLOR
            )

            track_name = track_info.get('name', track_name)
            artist_name = track_info.get('artist', {}).get('name', artist_name)
            user_playcount = int(track_info.get('userplaycount', 0))

            embed.add_field(name="Track", value=f"**{track_name}**", inline=False)
            embed.add_field(name="Artist", value=artist_name, inline=True)
            embed.add_field(name="Your Plays", value=f"{user_playcount:,}", inline=True)

            # Global playcount
            global_playcount = int(track_info.get('playcount', 0))
            if global_playcount > 0:
                embed.add_field(name="Global Plays", value=f"{global_playcount:,}", inline=True)

            # Album info
            album = track_info.get('album')
            if album and album.get('title'):
                embed.add_field(name="Album", value=album['title'], inline=True)

            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting plays for track {track_name} by {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching track play count.")

    @_lastfm.command(brief="Check your play count for an album")
    async def playsalbum(self, ctx, artist_name: str, *, album_name: str):
        """
        Usage: {0}lastfm playsalbum <artist> <album>
        Output: Show your play count for a specific album
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('album.getinfo', {
                'artist': artist_name,
                'album': album_name,
                'username': lastfm_username
            })

            album_info = data.get('album', {})
            if not album_info:
                await ctx.fail(f"Album '{album_name}' by '{artist_name}' not found.")
                return

            embed = discord.Embed(
                title="Album Play Count",
                color=EMBED_COLOR
            )

            album_name = album_info.get('name', album_name)
            artist_name = album_info.get('artist', artist_name)
            user_playcount = int(album_info.get('userplaycount', 0))

            embed.add_field(name="Album", value=f"**{album_name}**", inline=False)
            embed.add_field(name="Artist", value=artist_name, inline=True)
            embed.add_field(name="Your Plays", value=f"{user_playcount:,}", inline=True)

            # Global playcount
            global_playcount = int(album_info.get('playcount', 0))
            if global_playcount > 0:
                embed.add_field(name="Global Plays", value=f"{global_playcount:,}", inline=True)

            # Album image
            if album_info.get('image'):
                images = album_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting plays for album {album_name} by {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching album play count.")

    @_lastfm.command(brief="See how many plays you have for every track on an album")
    async def playsall(self, ctx, artist_name: str, *, album_name: str):
        """
        Usage: {0}lastfm playsall <artist> <album>
        Output: Show play counts for all tracks on an album
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            data = await lastfm_client.make_request('album.getinfo', {
                'artist': artist_name,
                'album': album_name,
                'username': lastfm_username
            })

            album_info = data.get('album', {})
            if not album_info:
                await ctx.fail(f"Album '{album_name}' by '{artist_name}' not found.")
                return

            tracks = album_info.get('tracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No tracks found for this album.")
                return

            # Format track play counts
            entries = []
            for track in tracks:
                track_name = track.get('name', 'Unknown Track')
                playcount = int(track.get('playcount', 0))
                entries.append(f"**{track_name}** - {playcount:,} plays")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"Track plays for {album_name} by {artist_name}"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting track plays for album {album_name} by {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching track play counts.")

    @_lastfm.command(brief="See your stats for a specific artist")
    async def overview(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm overview <artist>
        Output: Show comprehensive statistics for an artist
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            # Get artist info
            artist_data = await lastfm_client.make_request('artist.getinfo', {
                'artist': artist_name,
                'username': lastfm_username
            })

            artist_info = artist_data.get('artist', {})
            if not artist_info:
                await ctx.fail(f"Artist '{artist_name}' not found.")
                return

            # Get top tracks for this artist
            tracks_data = await lastfm_client.make_request('user.gettoptracks', {
                'user': lastfm_username,
                'period': 'overall',
                'limit': 5
            })

            embed = discord.Embed(
                title=f"Artist Overview: {artist_info.get('name', artist_name)}",
                color=EMBED_COLOR
            )

            # Play count
            user_playcount = int(artist_info.get('stats', {}).get('userplaycount', 0))
            embed.add_field(name="Your Plays", value=f"{user_playcount:,}", inline=True)

            # Global stats
            global_playcount = int(artist_info.get('stats', {}).get('playcount', 0))
            listeners = int(artist_info.get('stats', {}).get('listeners', 0))

            embed.add_field(name="Global Plays", value=f"{global_playcount:,}", inline=True)
            embed.add_field(name="Listeners", value=f"{listeners:,}", inline=True)

            # Top tracks by this artist from user's library
            top_tracks = tracks_data.get('toptracks', {}).get('track', [])
            if top_tracks:
                artist_tracks = [
                    track for track in top_tracks[:10]
                    if track.get('artist', {}).get('name', '').lower() == artist_name.lower()
                ][:5]

                if artist_tracks:
                    track_list = []
                    for track in artist_tracks:
                        track_name = track.get('name', 'Unknown')
                        playcount = int(track.get('playcount', 0))
                        track_list.append(f"**{track_name}** ({playcount:,} plays)")

                    embed.add_field(
                        name="Your Top Tracks",
                        value="\n".join(track_list),
                        inline=False
                    )

            # Artist image
            if artist_info.get('image'):
                images = artist_info['image']
                if images and isinstance(images, list):
                    large_image = next((img['#text'] for img in images if img['size'] == 'large'), None)
                    if large_image:
                        embed.set_thumbnail(url=large_image)

            # Bio summary
            bio = artist_info.get('bio', {})
            if bio and bio.get('summary'):
                summary = bio['summary'][:200] + "..." if len(bio['summary']) > 200 else bio['summary']
                # Remove HTML tags
                import re
                summary = re.sub('<[^<]+?>', '', summary)
                embed.add_field(name="About", value=summary, inline=False)

            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting overview for artist {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching artist overview.")

    @_lastfm.command(brief="View your most listened to artists")
    async def topartists(self, ctx, period: str = "overall", *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm topartists [period] [user]
        Output: Display top artists for a time period
        Periods: overall, 7day, 1month, 3month, 6month, 12month
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        # Validate period
        if period not in self.time_periods:
            await ctx.fail(f"Invalid period. Valid periods: {', '.join(self.time_periods.keys())}")
            return

        try:
            data = await lastfm_client.make_request('user.gettopartists', {
                'user': lastfm_username,
                'period': period,
                'limit': 50
            })

            artists = data.get('topartists', {}).get('artist', [])
            if not artists:
                await ctx.fail("No top artists found.")
                return

            # Format artists for pagination
            entries = []
            for artist in artists:
                artist_name = artist.get('name', 'Unknown Artist')
                playcount = int(artist.get('playcount', 0))
                entries.append(f"**{artist_name}** - {playcount:,} plays")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            period_name = period.replace('day', ' day').replace('month', ' month')
            view.embed.title = f"{user.display_name}'s Top Artists ({period_name})"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting top artists for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your top artists.")

    @_lastfm.command(brief="View your most listened to tracks")
    async def toptracks(self, ctx, period: str = "overall", *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm toptracks [period] [user]
        Output: Display top tracks for a time period
        Periods: overall, 7day, 1month, 3month, 6month, 12month
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        # Validate period
        if period not in self.time_periods:
            await ctx.fail(f"Invalid period. Valid periods: {', '.join(self.time_periods.keys())}")
            return

        try:
            data = await lastfm_client.make_request('user.gettoptracks', {
                'user': lastfm_username,
                'period': period,
                'limit': 50
            })

            tracks = data.get('toptracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No top tracks found.")
                return

            # Format tracks for pagination
            entries = []
            for track in tracks:
                track_name = track.get('name', 'Unknown Track')
                artist_name = track.get('artist', {})
                if isinstance(artist_name, dict):
                    artist_name = artist_name.get('name', 'Unknown Artist')
                playcount = int(track.get('playcount', 0))
                entries.append(f"**{track_name}** by {artist_name} - {playcount:,} plays")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            period_name = period.replace('day', ' day').replace('month', ' month')
            view.embed.title = f"{user.display_name}'s Top Tracks ({period_name})"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting top tracks for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your top tracks.")

    @_lastfm.command(brief="View your most listened to albums")
    async def topalbums(self, ctx, period: str = "overall", *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm topalbums [period] [user]
        Output: Display top albums for a time period
        Periods: overall, 7day, 1month, 3month, 6month, 12month
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        # Validate period
        if period not in self.time_periods:
            await ctx.fail(f"Invalid period. Valid periods: {', '.join(self.time_periods.keys())}")
            return

        try:
            data = await lastfm_client.make_request('user.gettopalbums', {
                'user': lastfm_username,
                'period': period,
                'limit': 50
            })

            albums = data.get('topalbums', {}).get('album', [])
            if not albums:
                await ctx.fail("No top albums found.")
                return

            # Format albums for pagination
            entries = []
            for album in albums:
                album_name = album.get('name', 'Unknown Album')
                artist_name = album.get('artist', {})
                if isinstance(artist_name, dict):
                    artist_name = artist_name.get('name', 'Unknown Artist')
                playcount = int(album.get('playcount', 0))
                entries.append(f"**{album_name}** by {artist_name} - {playcount:,} plays")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            period_name = period.replace('day', ' day').replace('month', ' month')
            view.embed.title = f"{user.display_name}'s Top Albums ({period_name})"
            view.message = await view.send_initial_message()

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting top albums for {lastfm_username}: {e}")
            await ctx.fail("An error occurred while fetching your top albums.")

    @_lastfm.command(brief="View your top 10 tracks for a specific artist")
    async def toptentracks(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm toptentracks <artist>
        Output: Show your top 10 tracks by a specific artist
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            # Get user's top tracks and filter by artist
            data = await lastfm_client.make_request('user.gettoptracks', {
                'user': lastfm_username,
                'period': 'overall',
                'limit': 200  # Get more to filter by artist
            })

            tracks = data.get('toptracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No tracks found.")
                return

            # Filter tracks by artist
            artist_tracks = []
            for track in tracks:
                track_artist = track.get('artist', {})
                if isinstance(track_artist, dict):
                    track_artist = track_artist.get('name', '')

                if artist_name.lower() in track_artist.lower():
                    track_name = track.get('name', 'Unknown Track')
                    playcount = int(track.get('playcount', 0))
                    artist_tracks.append(f"**{track_name}** - {playcount:,} plays")

                    if len(artist_tracks) >= 10:
                        break

            if not artist_tracks:
                await ctx.fail(f"No tracks found for artist '{artist_name}'.")
                return

            embed = discord.Embed(
                title=f"Top 10 tracks by {artist_name}",
                description="\n".join([f"{i+1}. {track}" for i, track in enumerate(artist_tracks)]),
                color=EMBED_COLOR
            )
            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting top tracks for artist {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching top tracks for this artist.")

    @_lastfm.command(brief="View your top 10 albums for a specific artist")
    async def toptenalbums(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm toptenalbums <artist>
        Output: Show your top 10 albums by a specific artist
        """
        lastfm_username = await self.get_lastfm_user(ctx, ctx.author)
        if not lastfm_username:
            return

        try:
            # Get user's top albums and filter by artist
            data = await lastfm_client.make_request('user.gettopalbums', {
                'user': lastfm_username,
                'period': 'overall',
                'limit': 200  # Get more to filter by artist
            })

            albums = data.get('topalbums', {}).get('album', [])
            if not albums:
                await ctx.fail("No albums found.")
                return

            # Filter albums by artist
            artist_albums = []
            for album in albums:
                album_artist = album.get('artist', {})
                if isinstance(album_artist, dict):
                    album_artist = album_artist.get('name', '')

                if artist_name.lower() in album_artist.lower():
                    album_name = album.get('name', 'Unknown Album')
                    playcount = int(album.get('playcount', 0))
                    artist_albums.append(f"**{album_name}** - {playcount:,} plays")

                    if len(artist_albums) >= 10:
                        break

            if not artist_albums:
                await ctx.fail(f"No albums found for artist '{artist_name}'.")
                return

            embed = discord.Embed(
                title=f"Top 10 albums by {artist_name}",
                description="\n".join([f"{i+1}. {album}" for i, album in enumerate(artist_albums)]),
                color=EMBED_COLOR
            )
            embed.set_footer(text=f"Last.fm • {ctx.author.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting top albums for artist {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching top albums for this artist.")

    @_lastfm.command(brief="View a list of your crowns")
    async def crowns(self, ctx):
        """
        Usage: {0}lastfm crowns
        Output: Show your artist crowns in this server
        """
        try:
            query = """
                    SELECT artist_name, playcount, last_updated
                    FROM lastfm_crowns
                    WHERE server_id = $1 AND user_id = $2
                    ORDER BY playcount DESC
                    """
            results = await self.bot.cxn.fetch(query, ctx.guild.id, ctx.author.id)

            if not results:
                await ctx.fail("You don't have any crowns in this server.")
                return

            # Format crowns for pagination
            entries = []
            for record in results:
                artist_name = record['artist_name']
                playcount = record['playcount']
                entries.append(f"**{artist_name}** - {playcount:,} plays")

            # Create pagination view
            view = LastFMPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{ctx.author.display_name}'s Crowns"
            view.embed.description = f"You have {len(entries)} crown{'s' if len(entries) != 1 else ''} in this server"
            view.message = await view.send_initial_message()

        except Exception as e:
            logger.error(f"Error getting crowns for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while fetching your crowns.")

    @_lastfm.command(brief="View the members with the most crowns")
    async def mostcrowns(self, ctx):
        """
        Usage: {0}lastfm mostcrowns
        Output: Show users with the most crowns in this server
        """
        try:
            query = """
                    SELECT user_id, COUNT(*) as crown_count
                    FROM lastfm_crowns
                    WHERE server_id = $1
                    GROUP BY user_id
                    ORDER BY crown_count DESC
                    LIMIT 20
                    """
            results = await self.bot.cxn.fetch(query, ctx.guild.id)

            if not results:
                await ctx.fail("No crowns found in this server.")
                return

            # Format crown counts
            entries = []
            for record in results:
                user_id = record['user_id']
                crown_count = record['crown_count']

                member = ctx.guild.get_member(user_id)
                if member:
                    entries.append(f"**{member.display_name}** - {crown_count:,} crown{'s' if crown_count != 1 else ''}")
                else:
                    entries.append(f"**Unknown User** - {crown_count:,} crown{'s' if crown_count != 1 else ''}")

            embed = discord.Embed(
                title="Most Crowns Leaderboard",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            embed.set_footer(text=f"Server: {ctx.guild.name}")
            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error getting crown leaderboard for server {ctx.guild.id}: {e}")
            await ctx.fail("An error occurred while fetching the crown leaderboard.")

    @_lastfm.command(brief="View top listeners for an artist in the server")
    async def whoknows(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm whoknows <artist>
        Output: Show who in the server listens to an artist the most
        """
        # Get all Last.fm users in the server
        query = """
                SELECT user_id, lastfm_username
                FROM lastfm_users
                WHERE user_id = ANY($1::BIGINT[])
                """
        member_ids = [member.id for member in ctx.guild.members if not member.bot]
        results = await self.bot.cxn.fetch(query, member_ids)

        if not results:
            await ctx.fail("No Last.fm users found in this server.")
            return

        user_plays = []

        for record in results:
            try:
                user_id = record['user_id']
                username = record['lastfm_username']

                # Get artist play count for this user
                data = await lastfm_client.make_request('artist.getinfo', {
                    'artist': artist_name,
                    'username': username
                })

                artist_info = data.get('artist', {})
                if artist_info:
                    playcount = int(artist_info.get('stats', {}).get('userplaycount', 0))
                    if playcount > 0:
                        member = ctx.guild.get_member(user_id)
                        if member:
                            user_plays.append((member.display_name, playcount, user_id))

            except Exception as e:
                logger.error(f"Error getting plays for user {user_id}: {e}")
                continue

        if not user_plays:
            await ctx.fail(f"No one in this server has listened to '{artist_name}'.")
            return

        # Sort by play count
        user_plays.sort(key=lambda x: x[1], reverse=True)

        # Update crowns if this is the top listener
        if user_plays:
            top_user = user_plays[0]
            crown_query = """
                        INSERT INTO lastfm_crowns (server_id, user_id, artist_name, playcount, last_updated)
                        VALUES ($1, $2, $3, $4, NOW() AT TIME ZONE 'UTC')
                        ON CONFLICT (server_id, artist_name)
                        DO UPDATE SET user_id = $2, playcount = $4, last_updated = NOW() AT TIME ZONE 'UTC'
                        WHERE lastfm_crowns.playcount < $4
                        """
            await self.bot.cxn.execute(
                crown_query,
                ctx.guild.id,
                top_user[2],
                artist_name,
                top_user[1]
            )

        # Format results
        entries = []
        for i, (name, plays, user_id) in enumerate(user_plays[:20]):
            crown_emoji = "👑 " if i == 0 else ""
            entries.append(f"{crown_emoji}**{name}** - {plays:,} plays")

        embed = discord.Embed(
            title=f"Who knows {artist_name}?",
            description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
            color=EMBED_COLOR
        )
        embed.set_footer(text=f"Server: {ctx.guild.name}")
        await ctx.send(embed=embed)

    @_lastfm.command(brief="View top listeners for an artist globally")
    async def globalwhoknows(self, ctx, *, artist_name: str):
        """
        Usage: {0}lastfm globalwhoknows <artist>
        Output: Show global top listeners for an artist
        """
        try:
            data = await lastfm_client.make_request('artist.gettopfans', {
                'artist': artist_name,
                'limit': 20
            })

            fans = data.get('topfans', {}).get('user', [])
            if not fans:
                await ctx.fail(f"No top fans found for '{artist_name}'.")
                return

            # Format fans
            entries = []
            for fan in fans:
                username = fan.get('name', 'Unknown')
                entries.append(f"**{username}**")

            embed = discord.Embed(
                title=f"Global top fans of {artist_name}",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting global whoknows for {artist_name}: {e}")
            await ctx.fail("An error occurred while fetching global top fans.")

    @_lastfm.command(brief="Top listeners for an album by an artist in the server")
    async def wkalbum(self, ctx, artist_name: str, *, album_name: str):
        """
        Usage: {0}lastfm wkalbum <artist> <album>
        Output: Show who in the server listens to an album the most
        """
        # Get all Last.fm users in the server
        query = """
                SELECT user_id, lastfm_username
                FROM lastfm_users
                WHERE user_id = ANY($1::BIGINT[])
                """
        member_ids = [member.id for member in ctx.guild.members if not member.bot]
        results = await self.bot.cxn.fetch(query, member_ids)

        if not results:
            await ctx.fail("No Last.fm users found in this server.")
            return

        user_plays = []

        for record in results:
            try:
                user_id = record['user_id']
                username = record['lastfm_username']

                # Get album play count for this user
                data = await lastfm_client.make_request('album.getinfo', {
                    'artist': artist_name,
                    'album': album_name,
                    'username': username
                })

                album_info = data.get('album', {})
                if album_info:
                    playcount = int(album_info.get('userplaycount', 0))
                    if playcount > 0:
                        member = ctx.guild.get_member(user_id)
                        if member:
                            user_plays.append((member.display_name, playcount))

            except Exception as e:
                logger.error(f"Error getting album plays for user {user_id}: {e}")
                continue

        if not user_plays:
            await ctx.fail(f"No one in this server has listened to '{album_name}' by '{artist_name}'.")
            return

        # Sort by play count
        user_plays.sort(key=lambda x: x[1], reverse=True)

        # Format results
        entries = []
        for name, plays in user_plays[:20]:
            entries.append(f"**{name}** - {plays:,} plays")

        embed = discord.Embed(
            title=f"Who knows {album_name} by {artist_name}?",
            description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
            color=EMBED_COLOR
        )
        embed.set_footer(text=f"Server: {ctx.guild.name}")
        await ctx.send(embed=embed)

    @_lastfm.command(brief="Top listeners for an album globally")
    async def globalwkalbum(self, ctx, artist_name: str, *, album_name: str):
        """
        Usage: {0}lastfm globalwkalbum <artist> <album>
        Output: Show global top listeners for an album
        """
        try:
            data = await lastfm_client.make_request('album.gettopfans', {
                'artist': artist_name,
                'album': album_name,
                'limit': 20
            })

            fans = data.get('topfans', {}).get('user', [])
            if not fans:
                await ctx.fail(f"No top fans found for '{album_name}' by '{artist_name}'.")
                return

            # Format fans
            entries = []
            for fan in fans:
                username = fan.get('name', 'Unknown')
                entries.append(f"**{username}**")

            embed = discord.Embed(
                title=f"Global top fans of {album_name} by {artist_name}",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting global album whoknows: {e}")
            await ctx.fail("An error occurred while fetching global top fans.")

    @_lastfm.command(brief="Top listeners for a track by an artist in the server")
    async def wktrack(self, ctx, artist_name: str, *, track_name: str):
        """
        Usage: {0}lastfm wktrack <artist> <track>
        Output: Show who in the server listens to a track the most
        """
        # Get all Last.fm users in the server
        query = """
                SELECT user_id, lastfm_username
                FROM lastfm_users
                WHERE user_id = ANY($1::BIGINT[])
                """
        member_ids = [member.id for member in ctx.guild.members if not member.bot]
        results = await self.bot.cxn.fetch(query, member_ids)

        if not results:
            await ctx.fail("No Last.fm users found in this server.")
            return

        user_plays = []

        for record in results:
            try:
                user_id = record['user_id']
                username = record['lastfm_username']

                # Get track play count for this user
                data = await lastfm_client.make_request('track.getinfo', {
                    'artist': artist_name,
                    'track': track_name,
                    'username': username
                })

                track_info = data.get('track', {})
                if track_info:
                    playcount = int(track_info.get('userplaycount', 0))
                    if playcount > 0:
                        member = ctx.guild.get_member(user_id)
                        if member:
                            user_plays.append((member.display_name, playcount))

            except Exception as e:
                logger.error(f"Error getting track plays for user {user_id}: {e}")
                continue

        if not user_plays:
            await ctx.fail(f"No one in this server has listened to '{track_name}' by '{artist_name}'.")
            return

        # Sort by play count
        user_plays.sort(key=lambda x: x[1], reverse=True)

        # Format results
        entries = []
        for name, plays in user_plays[:20]:
            entries.append(f"**{name}** - {plays:,} plays")

        embed = discord.Embed(
            title=f"Who knows {track_name} by {artist_name}?",
            description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
            color=EMBED_COLOR
        )
        embed.set_footer(text=f"Server: {ctx.guild.name}")
        await ctx.send(embed=embed)

    @_lastfm.command(brief="Top listeners for a track globally")
    async def globalwktrack(self, ctx, artist_name: str, *, track_name: str):
        """
        Usage: {0}lastfm globalwktrack <artist> <track>
        Output: Show global top listeners for a track
        """
        try:
            data = await lastfm_client.make_request('track.gettopfans', {
                'artist': artist_name,
                'track': track_name,
                'limit': 20
            })

            fans = data.get('topfans', {}).get('user', [])
            if not fans:
                await ctx.fail(f"No top fans found for '{track_name}' by '{artist_name}'.")
                return

            # Format fans
            entries = []
            for fan in fans:
                username = fan.get('name', 'Unknown')
                entries.append(f"**{username}**")

            embed = discord.Embed(
                title=f"Global top fans of {track_name} by {artist_name}",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting global track whoknows: {e}")
            await ctx.fail("An error occurred while fetching global top fans.")

    @_lastfm.command(brief="View server Last.fm reaction scoreboard")
    async def scoreboard(self, ctx):
        """
        Usage: {0}lastfm scoreboard
        Output: Show Last.fm reaction scoreboard for this server
        """
        try:
            query = """
                    SELECT user_id,
                           SUM(CASE WHEN reaction_type = 'upvote' THEN 1 ELSE 0 END) as upvotes,
                           SUM(CASE WHEN reaction_type = 'downvote' THEN 1 ELSE 0 END) as downvotes,
                           SUM(CASE WHEN reaction_type = 'upvote' THEN 1 ELSE -1 END) as score
                    FROM lastfm_reactions
                    WHERE server_id = $1
                    GROUP BY user_id
                    ORDER BY score DESC
                    LIMIT 20
                    """
            results = await self.bot.cxn.fetch(query, ctx.guild.id)

            if not results:
                await ctx.fail("No reaction data found for this server.")
                return

            # Format scoreboard
            entries = []
            for record in results:
                user_id = record['user_id']
                upvotes = record['upvotes']
                downvotes = record['downvotes']
                score = record['score']

                member = ctx.guild.get_member(user_id)
                if member:
                    entries.append(f"**{member.display_name}** - {score} pts ({upvotes}↑ {downvotes}↓)")
                else:
                    entries.append(f"**Unknown User** - {score} pts ({upvotes}↑ {downvotes}↓)")

            embed = discord.Embed(
                title="Last.fm Reaction Scoreboard",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            embed.set_footer(text=f"Server: {ctx.guild.name}")
            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error getting scoreboard for server {ctx.guild.id}: {e}")
            await ctx.fail("An error occurred while fetching the scoreboard.")

    @_lastfm.command(brief="View global Last.fm reaction board")
    async def globalboard(self, ctx):
        """
        Usage: {0}lastfm globalboard
        Output: Show global Last.fm reaction scoreboard
        """
        try:
            query = """
                    SELECT user_id,
                           SUM(CASE WHEN reaction_type = 'upvote' THEN 1 ELSE 0 END) as upvotes,
                           SUM(CASE WHEN reaction_type = 'downvote' THEN 1 ELSE 0 END) as downvotes,
                           SUM(CASE WHEN reaction_type = 'upvote' THEN 1 ELSE -1 END) as score
                    FROM lastfm_reactions
                    GROUP BY user_id
                    ORDER BY score DESC
                    LIMIT 20
                    """
            results = await self.bot.cxn.fetch(query)

            if not results:
                await ctx.fail("No global reaction data found.")
                return

            # Format global scoreboard
            entries = []
            for record in results:
                user_id = record['user_id']
                upvotes = record['upvotes']
                downvotes = record['downvotes']
                score = record['score']

                user = self.bot.get_user(user_id)
                if user:
                    entries.append(f"**{user.display_name}** - {score} pts ({upvotes}↑ {downvotes}↓)")
                else:
                    entries.append(f"**Unknown User** - {score} pts ({upvotes}↑ {downvotes}↓)")

            embed = discord.Embed(
                title="Global Last.fm Reaction Scoreboard",
                description="\n".join([f"{i+1}. {entry}" for i, entry in enumerate(entries)]),
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error getting global scoreboard: {e}")
            await ctx.fail("An error occurred while fetching the global scoreboard.")

    @_lastfm.command(brief="Set the embed color for Last.fm commands")
    async def color(self, ctx, color: str):
        """
        Usage: {0}lastfm color <hex_color>
        Output: Set your personal embed color for Last.fm commands
        """
        # Validate hex color
        if not color.startswith('#') or len(color) != 7:
            await ctx.fail("Please provide a valid hex color (e.g., #323339)")
            return

        try:
            int(color[1:], 16)  # Validate hex
        except ValueError:
            await ctx.fail("Please provide a valid hex color (e.g., #323339)")
            return

        try:
            query = """
                    INSERT INTO lastfm_settings (user_id, embed_color)
                    VALUES ($1, $2)
                    ON CONFLICT (user_id)
                    DO UPDATE SET embed_color = $2
                    """
            await self.bot.cxn.execute(query, ctx.author.id, color)

            embed = discord.Embed(
                title="Color Updated",
                description=f"Your Last.fm embed color has been set to {color}",
                color=int(color[1:], 16)
            )
            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error setting color for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while setting your color.")

    @_lastfm.command(brief="Change the embed layout for Now Playing")
    async def mode(self, ctx, mode: str):
        """
        Usage: {0}lastfm mode <mode>
        Output: Change your Now Playing embed layout
        Modes: default, minimal, detailed
        """
        valid_modes = ['default', 'minimal', 'detailed']
        if mode.lower() not in valid_modes:
            await ctx.fail(f"Invalid mode. Valid modes: {', '.join(valid_modes)}")
            return

        try:
            query = """
                    INSERT INTO lastfm_settings (user_id, now_playing_mode)
                    VALUES ($1, $2)
                    ON CONFLICT (user_id)
                    DO UPDATE SET now_playing_mode = $2
                    """
            await self.bot.cxn.execute(query, ctx.author.id, mode.lower())

            await ctx.success(f"Your Now Playing mode has been set to **{mode}**")

        except Exception as e:
            logger.error(f"Error setting mode for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while setting your mode.")

    @_lastfm.command(brief="Get a Spotify link for the current song")
    async def spotify(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm spotify [user]
        Output: Get Spotify link for currently playing or last played track
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get current/recent track
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 1
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            track = tracks[0] if isinstance(tracks, list) else tracks
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            # Create Spotify search URL
            search_query = f"{track_name} {artist_name}".replace(' ', '%20')
            spotify_url = f"https://open.spotify.com/search/{search_query}"

            embed = discord.Embed(
                title="Spotify Link",
                description=f"**{track_name}** by {artist_name}",
                color=EMBED_COLOR
            )
            embed.add_field(name="Search on Spotify", value=f"[Open Spotify]({spotify_url})", inline=False)
            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting Spotify link: {e}")
            await ctx.fail("An error occurred while getting the Spotify link.")

    @_lastfm.command(brief="Get a YouTube link for the current song")
    async def youtube(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm youtube [user]
        Output: Get YouTube link for currently playing or last played track
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get current/recent track
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 1
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            track = tracks[0] if isinstance(tracks, list) else tracks
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            # Create YouTube search URL
            search_query = f"{track_name} {artist_name}".replace(' ', '+')
            youtube_url = f"https://www.youtube.com/results?search_query={search_query}"

            embed = discord.Embed(
                title="YouTube Link",
                description=f"**{track_name}** by {artist_name}",
                color=EMBED_COLOR
            )
            embed.add_field(name="Search on YouTube", value=f"[Open YouTube]({youtube_url})", inline=False)
            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting YouTube link: {e}")
            await ctx.fail("An error occurred while getting the YouTube link.")

    @_lastfm.command(brief="Get an iTunes link for the current song")
    async def itunes(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm itunes [user]
        Output: Get iTunes link for currently playing or last played track
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get current/recent track
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 1
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            track = tracks[0] if isinstance(tracks, list) else tracks
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            # Create iTunes search URL
            search_query = f"{track_name} {artist_name}".replace(' ', '+')
            itunes_url = f"https://music.apple.com/search?term={search_query}"

            embed = discord.Embed(
                title="iTunes Link",
                description=f"**{track_name}** by {artist_name}",
                color=EMBED_COLOR
            )
            embed.add_field(name="Search on iTunes", value=f"[Open iTunes]({itunes_url})", inline=False)
            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting iTunes link: {e}")
            await ctx.fail("An error occurred while getting the iTunes link.")

    @_lastfm.command(brief="Get a SoundCloud link for the current song")
    async def soundcloud(self, ctx, *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm soundcloud [user]
        Output: Get SoundCloud link for currently playing or last played track
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        try:
            # Get current/recent track
            data = await lastfm_client.make_request('user.getrecenttracks', {
                'user': lastfm_username,
                'limit': 1
            })

            tracks = data.get('recenttracks', {}).get('track', [])
            if not tracks:
                await ctx.fail("No recent tracks found.")
                return

            track = tracks[0] if isinstance(tracks, list) else tracks
            track_name = track.get('name', 'Unknown Track')
            artist_name = track.get('artist', {})
            if isinstance(artist_name, dict):
                artist_name = artist_name.get('#text', 'Unknown Artist')

            # Create SoundCloud search URL
            search_query = f"{track_name} {artist_name}".replace(' ', '%20')
            soundcloud_url = f"https://soundcloud.com/search?q={search_query}"

            embed = discord.Embed(
                title="SoundCloud Link",
                description=f"**{track_name}** by {artist_name}",
                color=EMBED_COLOR
            )
            embed.add_field(name="Search on SoundCloud", value=f"[Open SoundCloud]({soundcloud_url})", inline=False)
            embed.set_footer(text=f"Last.fm • {user.display_name}")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting SoundCloud link: {e}")
            await ctx.fail("An error occurred while getting the SoundCloud link.")

    @_lastfm.command(brief="Generate a collage of your most listened albums")
    async def collage(self, ctx, period: str = "overall", size: str = "3x3", *, user: converters.DiscordMember = None):
        """
        Usage: {0}lastfm collage [period] [size] [user]
        Output: Generate an album collage
        Periods: overall, 7day, 1month, 3month, 6month, 12month
        Sizes: 3x3, 4x4, 5x5
        """
        user = user or ctx.author
        lastfm_username = await self.get_lastfm_user(ctx, user)
        if not lastfm_username:
            return

        # Validate period
        if period not in self.time_periods:
            await ctx.fail(f"Invalid period. Valid periods: {', '.join(self.time_periods.keys())}")
            return

        # Validate size
        valid_sizes = ['3x3', '4x4', '5x5']
        if size not in valid_sizes:
            await ctx.fail(f"Invalid size. Valid sizes: {', '.join(valid_sizes)}")
            return

        try:
            # Calculate number of albums needed
            grid_size = int(size[0])
            album_count = grid_size * grid_size

            # Get top albums
            data = await lastfm_client.make_request('user.gettopalbums', {
                'user': lastfm_username,
                'period': period,
                'limit': album_count
            })

            albums = data.get('topalbums', {}).get('album', [])
            if not albums:
                await ctx.fail("No albums found for collage generation.")
                return

            # For now, just show a text-based collage
            # In a full implementation, you would generate an actual image collage
            embed = discord.Embed(
                title=f"Album Collage ({size}) - {period}",
                color=EMBED_COLOR
            )

            collage_text = []
            for i, album in enumerate(albums[:album_count]):
                album_name = album.get('name', 'Unknown Album')
                artist_name = album.get('artist', {})
                if isinstance(artist_name, dict):
                    artist_name = artist_name.get('name', 'Unknown Artist')
                playcount = int(album.get('playcount', 0))

                collage_text.append(f"{i+1}. **{album_name}** by {artist_name} ({playcount:,} plays)")

            embed.description = "\n".join(collage_text)
            embed.set_footer(text=f"Last.fm • {user.display_name} • Note: Image collage generation coming soon!")
            await ctx.send(embed=embed)

        except LastFMError as e:
            await ctx.fail(f"Last.fm error: {str(e)}")
        except Exception as e:
            logger.error(f"Error generating collage: {e}")
            await ctx.fail("An error occurred while generating your collage.")
