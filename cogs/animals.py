import csv
import discord
import random
import logging
import aiohttp
import asyncio
from pathlib import Path
from typing import Optional, List, Dict, Any

from discord.ext import commands

from utilities import checks
from utilities import decorators

# Discord's ash color theme
EMBED_COLOR = 0x99AAB5

logger = logging.getLogger(__name__)


class AnimalAPIError(Exception):
    """Custom exception for animal API errors"""
    pass


async def read_shuffle_and_send(animal: str, ctx: commands.Context) -> None:
    """
    Read animal URLs from CSV file, shuffle, and send as embed.
    
    Args:
        animal: The animal type to fetch
        ctx: Discord command context
    """
    try:
        csv_path = Path(f"./data/csvs/{animal}_url.csv")
        
        if not csv_path.exists():
            logger.error(f"CSV file not found: {csv_path}")
            embed = discord.Embed(
                title="Error",
                description="Animal data not available at this time.",
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)
            return
        
        with open(csv_path, "r", encoding="utf-8") as f:
            reader = csv.reader(f)
            urls = [row[0] for row in reader if row and row[0].strip()]
        
        if not urls:
            logger.warning(f"No URLs found in {csv_path}")
            embed = discord.Embed(
                title="Error",
                description="No animal images available at this time.",
                color=EMBED_COLOR
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title=f"{animal.capitalize()}!",
            color=EMBED_COLOR
        )
        embed.set_image(url=random.choice(urls))
        
        # Add timestamp for freshness
        embed.timestamp = discord.utils.utcnow()
        
        await ctx.send(embed=embed)
        
    except Exception as e:
        logger.error(f"Error in read_shuffle_and_send for {animal}: {e}")
        embed = discord.Embed(
            title="Error",
            description="Something went wrong while fetching the animal image.",
            color=discord.Color.red()
        )
        await ctx.send(embed=embed)


async def fetch_json_api(
    session: aiohttp.ClientSession,
    url: str,
    timeout: int = 10
) -> Optional[Dict[str, Any]]:
    """
    Fetch JSON data from an API with proper error handling.
    
    Args:
        session: aiohttp session
        url: API endpoint URL
        timeout: Request timeout in seconds
        
    Returns:
        JSON response data or None if failed
    """
    try:
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
            if response.status == 200:
                return await response.json()
            else:
                logger.warning(f"API returned status {response.status} for {url}")
                return None
    except asyncio.TimeoutError:
        logger.error(f"Timeout while fetching {url}")
        return None
    except Exception as e:
        logger.error(f"Error fetching {url}: {e}")
        return None


class Animals(commands.Cog):
    """Get animal facts and pictures :)"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.session: Optional[aiohttp.ClientSession] = None

    async def cog_load(self) -> None:
        """Initialize aiohttp session when cog loads"""
        self.session = aiohttp.ClientSession()
        logger.info("Animals cog loaded with aiohttp session")

    async def cog_unload(self) -> None:
        """Clean up aiohttp session when cog unloads"""
        if self.session:
            await self.session.close()
            logger.info("Animals cog unloaded and session closed")

    async def _send_api_animal(
        self,
        ctx: commands.Context,
        api_url: str,
        title: str,
        image_key: str,
        footer_text: str,
        is_array: bool = False
    ) -> None:
        """
        Generic method for API-based animal commands.
        
        Args:
            ctx: Command context
            api_url: API endpoint URL
            title: Embed title
            image_key: JSON key for image URL
            footer_text: Footer text for attribution
            is_array: Whether the API returns an array of results
        """
        if not self.session:
            self.session = aiohttp.ClientSession()

        async with ctx.typing():
            try:
                data = await fetch_json_api(self.session, api_url)
                
                if not data:
                    embed = discord.Embed(
                        title="Error",
                        description=f"Unable to fetch {title.lower()} image at this time.",
                        color=EMBED_COLOR
                    )
                    await ctx.send(embed=embed)
                    return
                
                # Handle array responses (like TheCatAPI)
                if is_array and isinstance(data, list):
                    if not data or image_key not in data[0]:
                        embed = discord.Embed(
                            title="Error",
                            description=f"Unable to fetch {title.lower()} image at this time.",
                            color=EMBED_COLOR
                        )
                        await ctx.send(embed=embed)
                        return
                    image_url = data[0][image_key]
                else:
                    if image_key not in data:
                        embed = discord.Embed(
                            title="Error",
                            description=f"Unable to fetch {title.lower()} image at this time.",
                            color=EMBED_COLOR
                        )
                        await ctx.send(embed=embed)
                        return
                    image_url = data[image_key]
                
                embed = discord.Embed(title=title, color=EMBED_COLOR)
                embed.set_image(url=image_url)
                embed.set_footer(text=footer_text)
                embed.timestamp = discord.utils.utcnow()
                
                await ctx.send(embed=embed)
                
            except Exception as e:
                logger.error(f"Error in _send_api_animal for {title}: {e}")
                embed = discord.Embed(
                    title="Error",
                    description="Something went wrong while fetching the animal image.",
                    color=discord.Color.red()
                )
                await ctx.send(embed=embed)

    @decorators.command(
        brief="Random picture of a cat",
        aliases=["cats", "meow"],
    )
    @checks.cooldown()
    async def cat(self, ctx: commands.Context) -> None:
        """Get a random cat picture"""
        await self._send_api_animal(
            ctx,
            "https://api.thecatapi.com/v1/images/search",
            "Meow",
            "url",
            "https://thecatapi.com/",
            is_array=True
        )

    @decorators.command(brief="Random picture of a fox")
    @checks.cooldown()
    async def fox(self, ctx: commands.Context) -> None:
        """Get a random fox picture"""
        await self._send_api_animal(
            ctx,
            "https://randomfox.ca/floof/",
            "Floof",
            "image",
            "https://randomfox.ca/"
        )

    @decorators.command(
        brief="Random picture of a duck",
        aliases=["quack", "ducc", "ducks"]
    )
    @checks.cooldown()
    async def duck(self, ctx: commands.Context) -> None:
        """Get a random duck picture"""
        await self._send_api_animal(
            ctx,
            "https://random-d.uk/api/random",
            "Quack",
            "url",
            "https://random-d.uk/"
        )

    @decorators.command(
        brief="Random picture of a dog",
        aliases=["dogs", "bark"]
    )
    @checks.cooldown()
    async def dog(self, ctx: commands.Context) -> None:
        """Get a random dog picture"""
        await read_shuffle_and_send("dog", ctx)

    @decorators.command(
        brief="Random picture of a panda (may be a red panda)",
        aliases=["pandas"]
    )
    @checks.cooldown()
    async def panda(self, ctx: commands.Context) -> None:
        """Get a random panda picture (may be a red panda)"""
        await read_shuffle_and_send("panda", ctx)

    @decorators.command(brief="Random picture of a pig", aliases=["pigs"])
    @checks.cooldown()
    async def pig(self, ctx: commands.Context) -> None:
        """Get a random pig picture"""
        await read_shuffle_and_send("pig", ctx)

    @decorators.command(
        brief="Random picture of a possum",
        aliases=["possums"]
    )
    @checks.cooldown()
    async def possum(self, ctx: commands.Context) -> None:
        """Get a random possum picture"""
        await read_shuffle_and_send("possum", ctx)

    @decorators.command(brief="Random picture of a red panda", aliases=["redpandas"])
    @checks.cooldown()
    async def redpanda(self, ctx: commands.Context) -> None:
        """Get a random red panda picture"""
        await read_shuffle_and_send("redpanda", ctx)

    @decorators.command(brief="Random picture of a sheep", aliases=["shep"])
    @checks.cooldown()
    async def sheep(self, ctx: commands.Context) -> None:
        """Get a random sheep picture"""
        await read_shuffle_and_send("sheep", ctx)

    @decorators.command(brief="Random picture of a snake", aliases=["snek", "snakes"])
    @checks.cooldown()
    async def snake(self, ctx: commands.Context) -> None:
        """Get a random snake picture"""
        await read_shuffle_and_send("snek", ctx)

    @decorators.command(
        brief="Random picture of a squirrel",
        aliases=["squ", "squirrels"]
    )
    @checks.cooldown()
    async def squirrel(self, ctx: commands.Context) -> None:
        """Get a random squirrel picture"""
        await read_shuffle_and_send("squirrel", ctx)

    @decorators.command(
        brief="Random picture of a bear",
        aliases=["bears"]
    )
    @checks.cooldown()
    async def bear(self, ctx: commands.Context) -> None:
        """Get a random bear picture"""
        await read_shuffle_and_send("bear", ctx)

    @decorators.command(
        brief="Random picture of a bird",
        aliases=["birb"]
    )
    @checks.cooldown()
    async def bird(self, ctx: commands.Context) -> None:
        """Get a random bird picture"""
        await read_shuffle_and_send("bird", ctx)

    @decorators.command(
        brief="Random picture of a bunny",
        aliases=["bunnies", "rabbit"]
    )
    @checks.cooldown()
    async def bunny(self, ctx: commands.Context) -> None:
        """Get a random bunny picture"""
        await read_shuffle_and_send("bunny", ctx)

    @decorators.command(brief="Random animal fact", aliases=["anifact"])
    @checks.cooldown()
    async def animalfact(self, ctx: commands.Context) -> None:
        """Get a random animal fact"""
        try:
            facts_path = Path("data/csvs/facts.csv")
            
            if not facts_path.exists():
                embed = discord.Embed(
                    title="Error",
                    description="Animal facts not available at this time.",
                    color=EMBED_COLOR
                )
                await ctx.send(embed=embed)
                return
            
            with open(facts_path, "r", encoding="utf-8") as f:
                facts = [line.strip() for line in f.readlines() if line.strip()]
            
            if not facts:
                embed = discord.Embed(
                    title="Error",
                    description="No animal facts available at this time.",
                    color=EMBED_COLOR
                )
                await ctx.send(embed=embed)
                return
            
            fact = random.choice(facts)
            
            embed = discord.Embed(
                title="🐾 Animal Fact",
                description=fact,
                color=EMBED_COLOR
            )
            embed.timestamp = discord.utils.utcnow()
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in animalfact command: {e}")
            embed = discord.Embed(
                title="Error",
                description="Something went wrong while fetching an animal fact.",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)


async def setup(bot: commands.Bot) -> None:
    """Setup function for loading the cog"""
    await bot.add_cog(Animals(bot))