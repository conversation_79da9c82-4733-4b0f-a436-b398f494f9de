import discord
from datetime import datetime
from discord.ext import commands

from utilities import utils
from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(Welcome(bot))


class Welcome(commands.Cog):
    """
    Manage welcome and goodbye messages.
    """

    def __init__(self, bot):
        self.bot = bot

    #####################
    ## Event Listeners ##
    #####################

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_member_join(self, member):
        """Handle member join events for welcome messages"""
        if member.bot:
            return
            
        # Get welcome messages for this server
        query = """
                SELECT channel_id, message
                FROM welcome_messages
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, member.guild.id)
        
        for record in records:
            channel = self.bot.get_channel(record['channel_id'])
            if not channel:
                continue
                
            try:
                # Parse and send the welcome message
                content, embed, view = await self.parse_message(record['message'], member, channel)
                if embed and view:
                    await channel.send(content=content, embed=embed, view=view)
                elif embed:
                    await channel.send(content=content, embed=embed)
                elif view:
                    await channel.send(content=content, view=view)
                else:
                    await channel.send(content=content)
            except Exception as e:
                print(f"Error sending welcome message: {e}")

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_member_remove(self, member):
        """Handle member leave events for goodbye messages and welcome message deletion"""
        if member.bot:
            return
            
        # Check if we should delete welcome messages on leave
        query = """
                SELECT delete_on_leave
                FROM welcome_settings
                WHERE server_id = $1;
                """
        setting = await self.bot.cxn.fetchval(query, member.guild.id)
        
        if setting:
            # Delete welcome messages for this user (implementation would need message tracking)
            pass
            
        # Get goodbye messages for this server
        query = """
                SELECT channel_id, message
                FROM goodbye_messages
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, member.guild.id)
        
        for record in records:
            channel = self.bot.get_channel(record['channel_id'])
            if not channel:
                continue
                
            try:
                # Parse and send the goodbye message
                content, embed, view = await self.parse_message(record['message'], member, channel)
                if embed and view:
                    await channel.send(content=content, embed=embed, view=view)
                elif embed:
                    await channel.send(content=content, embed=embed)
                elif view:
                    await channel.send(content=content, view=view)
                else:
                    await channel.send(content=content)
            except Exception as e:
                print(f"Error sending goodbye message: {e}")

    ######################
    ## Helper Functions ##
    ######################

    async def parse_message(self, message, member, channel):
        """Parse message with variables and return content, embed, and view"""
        # Check if message contains embed syntax BEFORE replacing variables
        if "{embed}" in message:
            return await self.parse_embed_message(message, member, channel)
        else:
            # Only replace variables for non-embed messages
            parsed_message = await self.replace_variables(message, member, channel)
            return parsed_message, None, None

    async def parse_embed_message(self, message, member, channel):
        """Parse embed syntax from message"""
        # Remove {embed} tag
        message = message.replace("{embed}", "").strip()

        embed = discord.Embed(color=0x323339)
        content = None
        buttons = []

        # Parse $v{property: value} patterns manually to handle nested braces
        def extract_embed_components(text):
            components = {}
            i = 0
            while i < len(text):
                # Look for $v{ pattern
                if text[i:i+3] == '$v{':
                    # Find the property name (everything before the colon)
                    start = i + 3
                    colon_pos = text.find(':', start)
                    if colon_pos == -1:
                        i += 1
                        continue

                    prop = text[start:colon_pos].strip()

                    # Find the matching closing brace, handling nested braces
                    brace_count = 1
                    value_start = colon_pos + 1
                    j = value_start

                    while j < len(text) and brace_count > 0:
                        if text[j] == '{':
                            brace_count += 1
                        elif text[j] == '}':
                            brace_count -= 1
                        j += 1

                    if brace_count == 0:
                        value = text[value_start:j-1].strip()
                        components[prop] = value
                        # Remove this component from the text
                        text = text[:i] + text[j:]
                        # Reset index to start over since we modified the text
                        i = 0
                        continue

                i += 1

            return components, text

        components, remaining_text = extract_embed_components(message)

        # Process each component
        for prop, value in components.items():
            # Replace variables in the value
            value = await self.replace_variables(value, member, channel)

            if prop == 'description':
                embed.description = value
            elif prop == 'title':
                embed.title = value
            elif prop == 'thumbnail':
                embed.set_thumbnail(url=value)
            elif prop == 'image':
                embed.set_image(url=value)
            elif prop == 'footer':
                embed.set_footer(text=value)
            elif prop == 'author':
                embed.set_author(name=value)
            elif prop == 'color':
                try:
                    color_int = int(value.replace('#', ''), 16)
                    embed.color = color_int
                except:
                    pass
            elif prop == 'message':
                content = value  # Value is already processed by replace_variables above
            elif prop == 'button':
                # Parse button format: label && url && style (optional)
                button_parts = [part.strip() for part in value.split('&&')]
                if len(button_parts) >= 2:
                    label = button_parts[0]
                    url = button_parts[1]
                    style = button_parts[2] if len(button_parts) > 2 else 'link'

                    button_info = {
                        'label': label,
                        'url': url,
                        'style': style
                    }
                    buttons.append(button_info)

        # Create view with buttons if any exist
        view = None
        if buttons:
            view = discord.ui.View(timeout=None)
            for button_info in buttons:
                if button_info['url'].startswith('http'):
                    # URL button
                    button = discord.ui.Button(
                        label=button_info['label'],
                        url=button_info['url'],
                        style=discord.ButtonStyle.link
                    )
                    view.add_item(button)

        # Any remaining text becomes content if no message was specified
        remaining_text = remaining_text.strip()
        if remaining_text and not content:
            content = await self.replace_variables(remaining_text, member, channel)

        return content, embed, view

    async def replace_variables(self, text, member, channel):
        """Replace all variables in text with actual values"""
        guild = member.guild
        # Use local timezone instead of UTC
        now = datetime.now()

        # Get member join position
        join_position = 1
        if hasattr(member, 'joined_at') and member.joined_at:
            members_before = [m for m in guild.members if m.joined_at and m.joined_at < member.joined_at]
            join_position = len(members_before) + 1

        # Get join position suffix
        def get_suffix(num):
            if 10 <= num % 100 <= 20:
                return 'th'
            else:
                return {1: 'st', 2: 'nd', 3: 'rd'}.get(num % 10, 'th')

        join_position_suffix = f"{join_position}{get_suffix(join_position)}"

        # User variables
        user_discriminator = getattr(member, 'discriminator', '0')
        user_tag = f"#{user_discriminator}" if user_discriminator != '0' else ''
        user_variables = {
            '{user}': f"{member.name}{user_tag}",
            '{user.id}': str(member.id),
            '{user.mention}': member.mention,
            '{user.name}': member.name,
            '{user.tag}': user_discriminator,
            '{user.avatar}': str(member.avatar.url) if member.avatar else str(getattr(member, 'default_avatar', member.display_avatar).url),
            '{user.guild_avatar}': str(member.guild_avatar.url) if getattr(member, 'guild_avatar', None) else str(member.display_avatar.url),
            '{user.display_avatar}': str(member.display_avatar.url),
            '{user.joined_at}': member.joined_at.strftime('%Y-%m-%d %H:%M:%S UTC') if member.joined_at else 'N/A',
            '{user.joined_at_timestamp}': str(int(member.joined_at.timestamp())) if member.joined_at else 'N/A',
            '{user.created_at}': member.created_at.strftime('%Y-%m-%d %H:%M:%S UTC'),
            '{user.created_at_timestamp}': str(int(member.created_at.timestamp())),
            '{user.display_name}': member.display_name,
            '{user.boost}': 'Yes' if member.premium_since else 'No',
            '{user.boost_since}': member.premium_since.strftime('%Y-%m-%d %H:%M:%S UTC') if member.premium_since else 'N/A',
            '{user.boost_since_timestamp}': str(int(member.premium_since.timestamp())) if member.premium_since else 'N/A',
            '{user.color}': str(member.color),
            '{user.top_role}': member.top_role.name if member.top_role.name != '@everyone' else 'N/A',
            '{user.role_list}': ', '.join([role.mention for role in member.roles[1:]]) if len(member.roles) > 1 else 'N/A',
            '{user.role_text_list}': ', '.join([role.name for role in member.roles[1:]]) if len(member.roles) > 1 else 'N/A',
            '{user.bot}': 'Yes' if member.bot else 'No',
            '{user.badges_icons}': 'N/A',  # Would need to implement badge detection
            '{user.badges}': 'N/A',  # Would need to implement badge detection
            '{user.join_position}': str(join_position),
            '{user.join_position_suffix}': join_position_suffix,
        }

        # Guild variables
        guild_variables = {
            '{guild.name}': guild.name,
            '{guild.id}': str(guild.id),
            '{guild.count}': str(guild.member_count),
            '{guild.region}': str(guild.preferred_locale) if hasattr(guild, 'preferred_locale') else 'N/A',
            '{guild.shard}': str(guild.shard_id) if guild.shard_id else '0',
            '{guild.owner_id}': str(guild.owner_id),
            '{guild.created_at}': guild.created_at.strftime('%Y-%m-%d %H:%M:%S UTC'),
            '{guild.created_at_timestamp}': str(int(guild.created_at.timestamp())),
            '{guild.emoji_count}': str(len(guild.emojis)),
            '{guild.role_count}': str(len(guild.roles)),
            '{guild.boost_count}': str(guild.premium_subscription_count or 0),
            '{guild.boost_tier}': f'Level {guild.premium_tier}' if guild.premium_tier else 'No Level',
            '{guild.preferred_locale}': str(guild.preferred_locale),
            '{guild.key_features}': ', '.join(guild.features) if guild.features else 'N/A',
            '{guild.icon}': str(guild.icon.url) if guild.icon else 'N/A',
            '{guild.banner}': str(guild.banner.url) if guild.banner else 'N/A',
            '{guild.splash}': str(guild.splash.url) if guild.splash else 'N/A',
            '{guild.discovery}': str(guild.discovery_splash.url) if guild.discovery_splash else 'N/A',
            '{guild.max_presences}': str(guild.max_presences or 'N/A'),
            '{guild.max_members}': str(guild.max_members or 'N/A'),
            '{guild.max_video_channel_users}': str(guild.max_video_channel_users or 'N/A'),
            '{guild.afk_timeout}': str(guild.afk_timeout),
            '{guild.afk_channel}': guild.afk_channel.name if guild.afk_channel else 'N/A',
            '{guild.channels}': ', '.join([c.name for c in guild.channels]) if guild.channels else 'N/A',
            '{guild.channels_count}': str(len(guild.channels)),
            '{guild.text_channels}': ', '.join([c.name for c in guild.text_channels]),
            '{guild.text_channels_count}': str(len(guild.text_channels)),
            '{guild.voice_channels}': ', '.join([c.name for c in guild.voice_channels]),
            '{guild.voice_channels_count}': str(len(guild.voice_channels)),
            '{guild.category_channels}': ', '.join([c.name for c in guild.categories]),
            '{guild.category_channels_count}': str(len(guild.categories)),
            '{guild.vanity}': guild.vanity_url_code or 'N/A',
        }

        # Channel variables
        channel_variables = {
            '{channel.name}': channel.name,
            '{channel.id}': str(channel.id),
            '{channel.mention}': channel.mention,
            '{channel.topic}': getattr(channel, 'topic', 'N/A') or 'N/A',
            '{channel.type}': str(channel.type),
            '{channel.category_id}': str(channel.category.id) if getattr(channel, 'category', None) else 'N/A',
            '{channel.category_name}': channel.category.name if getattr(channel, 'category', None) else 'N/A',
            '{channel.position}': str(getattr(channel, 'position', 0)),
            '{channel.slowmode_delay}': str(getattr(channel, 'slowmode_delay', 0)),
        }

        # Date & Time variables (simplified for now)
        datetime_variables = {
            '{date.now}': now.strftime('%Y-%m-%d'),
            '{date.utc_timestamp}': str(int(now.timestamp())),
            '{date.now_proper}': now.strftime('%B %d, %Y'),
            '{date.now_short}': now.strftime('%m/%d/%Y'),
            '{date.now_shorter}': now.strftime('%m/%d'),
            '{time.now}': now.strftime('%I:%M %p'),
            '{time.now_military}': now.strftime('%H:%M'),
            '{date.utc_now}': now.strftime('%Y-%m-%d'),
            '{date.utc_now_proper}': now.strftime('%B %d, %Y'),
            '{date.utc_now_short}': now.strftime('%m/%d/%Y'),
            '{date.utc_now_shorter}': now.strftime('%m/%d'),
            '{time.utc_now}': now.strftime('%I:%M %p'),
            '{time.utc_now_military}': now.strftime('%H:%M'),
        }

        # Boost variables
        boost_variables = {
            '{boost.count}': '1' if member.premium_since else '0',
            '{guild.boost_count}': str(guild.premium_subscription_count or 0),
        }

        # Button variables (for creating clickable buttons)
        button_variables = {
            '{button.invite}': f'https://discord.com/api/oauth2/authorize?client_id={self.bot.user.id}&permissions=8&scope=bot',
            '{button.support}': 'https://discord.gg/support',  # Replace with actual support server
            '{button.website}': 'https://example.com',  # Replace with actual website
            '{button.rules}': f'https://discord.com/channels/{guild.id}',  # Guild link
            '{button.chat}': f'https://discord.com/channels/{guild.id}',  # Guild link
        }

        # Combine all variables
        all_variables = {**user_variables, **guild_variables, **channel_variables, **datetime_variables, **boost_variables, **button_variables}

        # Replace all variables
        for var, value in all_variables.items():
            text = text.replace(var, value)

        return text

    ######################
    ## Welcome Commands ##
    ######################

    @decorators.group(
        invoke_without_command=True,
        brief="Set up welcome messages in channels.",
        implemented="2024-01-01 00:00:00.000000",
        updated="2024-01-01 00:00:00.000000",
        aliases=["welcomer"]
    )
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet(self, ctx):
        """
        Usage: {0}greet
        Alias: {0}welcomer
        Permission: Manage Guild
        Output:
            Shows welcome command help
        """
        await ctx.send_help(ctx.command)

    @greet.command(name="add")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet_add(self, ctx, channel: converters.DiscordChannel, *, message):
        """
        Usage: {0}greet add <channel> <message>
        Alias: {0}welcomer add <channel> <message>
        Permission: Manage Guild
        Output:
            Adds a welcome message for the specified channel
        """
        if not isinstance(channel, (discord.TextChannel, discord.Thread)):
            return await ctx.fail("Channel must be a text channel.")

        # Insert or update welcome message
        query = """
                INSERT INTO welcome_messages (server_id, channel_id, message)
                VALUES ($1, $2, $3)
                ON CONFLICT (server_id, channel_id)
                DO UPDATE SET message = $3, created_at = NOW();
                """
        await self.bot.cxn.execute(query, ctx.guild.id, channel.id, message)

        await ctx.success(f"Welcome message set for {channel.mention}")

    @greet.command(name="remove")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def greet_remove(self, ctx, channel: converters.DiscordChannel):
        """
        Usage: {0}greet remove <channel>
        Alias: {0}welcomer remove <channel>
        Permission: Manage Guild
        Output:
            Removes the welcome message from the specified channel
        """
        query = """
                DELETE FROM welcome_messages
                WHERE server_id = $1 AND channel_id = $2;
                """
        result = await self.bot.cxn.execute(query, ctx.guild.id, channel.id)

        if result == "DELETE 0":
            return await ctx.fail(f"No welcome message found for {channel.mention}")

        await ctx.success(f"Welcome message removed from {channel.mention}")

    @greet.command(name="view")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet_view(self, ctx, channel: converters.DiscordChannel):
        """
        Usage: {0}greet view <channel>
        Alias: {0}welcome view <channel>
        Permission: Manage Guild
        Output:
            Shows the welcome message for the specified channel
        """
        query = """
                SELECT message
                FROM welcome_messages
                WHERE server_id = $1 AND channel_id = $2;
                """
        message = await self.bot.cxn.fetchval(query, ctx.guild.id, channel.id)

        if not message:
            return await ctx.fail(f"No welcome message found for {channel.mention}")

        embed = discord.Embed(
            title=f"Welcome Message for {channel.name}",
            description=f"```\n{message}\n```",
            color=0x323339
        )

        await ctx.send(embed=embed)

    @greet.command(name="list")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet_list(self, ctx):
        """
        Usage: {0}greet list
        Alias: {0}welcome list
        Permission: Manage Guild
        Output:
            Shows all welcome messages in the server
        """
        query = """
                SELECT channel_id, message
                FROM welcome_messages
                WHERE server_id = $1
                ORDER BY created_at;
                """
        records = await self.bot.cxn.fetch(query, ctx.guild.id)

        if not records:
            return await ctx.fail("No welcome messages found in this server.")

        embed = discord.Embed(
            title="Welcome Messages",
            color=0x323339
        )

        for record in records:
            channel = self.bot.get_channel(record['channel_id'])
            channel_name = channel.mention if channel else f"<#{record['channel_id']}>"
            message_preview = record['message'][:100] + "..." if len(record['message']) > 100 else record['message']
            embed.add_field(
                name=channel_name,
                value=f"```\n{message_preview}\n```",
                inline=False
            )

        await ctx.send(embed=embed)

    @greet.command(name="clear")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def greet_clear(self, ctx):
        """
        Usage: {0}greet clear
        Alias: {0}welcome clear
        Permission: Manage Guild
        Output:
            Removes all welcome messages from the server
        """
        if not await ctx.confirm("This will remove all welcome messages from this server."):
            return

        query = """
                DELETE FROM welcome_messages
                WHERE server_id = $1;
                """
        result = await self.bot.cxn.execute(query, ctx.guild.id)

        count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
        await ctx.success(f"Removed {count} welcome message{'s' if count != 1 else ''}")

    @greet.command(name="removal")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def greet_removal(self, ctx):
        """
        Usage: {0}greet removal
        Alias: {0}welcome removal
        Permission: Manage Guild
        Output:
            Toggles welcome message deletion when members leave
        """
        query = """
                INSERT INTO welcome_settings (server_id, delete_on_leave)
                VALUES ($1, TRUE)
                ON CONFLICT (server_id)
                DO UPDATE SET delete_on_leave = NOT welcome_settings.delete_on_leave
                RETURNING delete_on_leave;
                """
        enabled = await self.bot.cxn.fetchval(query, ctx.guild.id)

        status = "enabled" if enabled else "disabled"
        await ctx.success(f"Welcome message deletion on member leave {status}")

    @greet.command(name="variables")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet_variables(self, ctx):
        """
        Usage: {0}greet variables
        Alias: {0}welcome variables
        Permission: Manage Guild
        Output:
            Shows all available variables for welcome messages
        """
        embed = discord.Embed(
            title="Welcome Message Variables",
            color=0x323339
        )

        # User variables
        user_vars = [
            ("{user}", "The user's name and discriminator"),
            ("{user.id}", "The user's ID"),
            ("{user.mention}", "The user's mention"),
            ("{user.name}", "The user's name"),
            ("{user.tag}", "The user's discriminator"),
            ("{user.avatar}", "The user's profile picture"),
            ("{user.guild_avatar}", "The user's guild profile picture"),
            ("{user.display_avatar}", "The user's available profile picture"),
            ("{user.joined_at}", "The user's guild join date in UTC"),
            ("{user.joined_at_timestamp}", "The user's guild join date in UNIX"),
            ("{user.created_at}", "The user's account creation date in UTC"),
            ("{user.created_at_timestamp}", "The user's account creation date in UNIX"),
            ("{user.display_name}", "The user's current display name"),
            ("{user.boost}", "Yes/No if the user is boosting the guild"),
            ("{user.boost_since}", "The user's initial guild boost date"),
            ("{user.boost_since_timestamp}", "The user's initial guild boost date in UNIX"),
            ("{user.color}", "The user's top role hex code"),
            ("{user.top_role}", "The user's top role name, defaults to N/A"),
            ("{user.role_list}", "The user's role list (possibly N/A)"),
            ("{user.role_text_list}", "The user's role list in text (possibly N/A)"),
            ("{user.bot}", "Yes/No if the user is a bot"),
            ("{user.badges_icons}", "The user's profile badges with emotes (possibly N/A)"),
            ("{user.badges}", "The user's profile badges in text (possibly N/A)"),
            ("{user.join_position}", "The user's join position"),
            ("{user.join_position_suffix}", "The user's join position with suffix")
        ]

        embed.add_field(
            name="User Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in user_vars[:10]]),
            inline=False
        )

        # Guild variables
        guild_vars = [
            ("{guild.name}", "The guild's name"),
            ("{guild.id}", "The guild's ID"),
            ("{guild.count}", "The guild's member count"),
            ("{guild.region}", "The guild's voice region"),
            ("{guild.shard}", "The guild's shard ID on bot"),
            ("{guild.owner_id}", "The guild's owner ID"),
            ("{guild.created_at}", "The guild's creation date in UTC"),
            ("{guild.created_at_timestamp}", "The guild's creation date in UNIX"),
            ("{guild.emoji_count}", "The guild's emoji count"),
            ("{guild.role_count}", "The guild's role count"),
            ("{guild.boost_count}", "The guild's boost count"),
            ("{guild.boost_tier}", "The guild's boost tier, defaults to No Level if none"),
            ("{guild.preferred_locale}", "The guild's preferred locale"),
            ("{guild.key_features}", "The guild's list of features (possibly N/A)"),
            ("{guild.icon}", "The guild's icon URL as a PNG (possibly N/A)"),
            ("{guild.banner}", "The guild's banner URL as a PNG (possibly N/A)"),
            ("{guild.splash}", "The guild's splash URL as a PNG (possibly N/A)"),
            ("{guild.discovery}", "The guild's discovery splash URL as a PNG (possibly N/A)"),
            ("{guild.max_presences}", "The guild's max presences amount"),
            ("{guild.max_members}", "The guild's max members amount"),
            ("{guild.max_video_channel_users}", "The guild's max video channel users"),
            ("{guild.afk_timeout}", "The guild's AFK timeout in seconds"),
            ("{guild.afk_channel}", "The guild's AFK channel (possibly N/A)"),
            ("{guild.channels}", "The guild's list of text, voice & category channels (possibly N/A)"),
            ("{guild.channels_count}", "The guild's total channel count"),
            ("{guild.text_channels}", "The guild's list of text channels"),
            ("{guild.text_channels_count}", "The guild's text channel count"),
            ("{guild.voice_channels}", "The guild's list of voice channels"),
            ("{guild.voice_channels_count}", "The guild's voice channel count"),
            ("{guild.category_channels}", "The guild's list of category channels"),
            ("{guild.category_channels_count}", "The guild's category channel count"),
            ("{guild.vanity}", "The guild's custom vanity url")
        ]

        embed.add_field(
            name="Guild Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in guild_vars[:10]]),
            inline=False
        )

        # Channel variables
        channel_vars = [
            ("{channel.name}", "The channel's name"),
            ("{channel.id}", "The channel's ID"),
            ("{channel.mention}", "The channel's mention"),
            ("{channel.topic}", "The channel's topic"),
            ("{channel.type}", "The channel's type (text/news/..)"),
            ("{channel.category_id}", "The channel's category ID (possibly N/A)"),
            ("{channel.category_name}", "The channel's category name (possibly N/A)"),
            ("{channel.position}", "The channel's position in the guild channel list"),
            ("{channel.slowmode_delay}", "The channel's slowmode delay in seconds (defaults to 0)")
        ]

        embed.add_field(
            name="Channel Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in channel_vars]),
            inline=False
        )

        # Date & Time variables
        datetime_vars = [
            ("{date.now}", "Current date"),
            ("{date.utc_timestamp}", "Current date as UNIX timestamp"),
            ("{date.now_proper}", "Better formatted date"),
            ("{date.now_short}", "Short formatted date"),
            ("{date.now_shorter}", "Shorter formatted date"),
            ("{time.now}", "12 hour timestamp"),
            ("{time.now_military}", "24 hour timestamp"),
            ("{date.utc_now}", "Current date"),
            ("{date.utc_now_proper}", "Better formatted date"),
            ("{date.utc_now_short}", "Short formatted date"),
            ("{date.utc_now_shorter}", "Shorter formatted date"),
            ("{time.utc_now}", "12 hour timestamp"),
            ("{time.utc_now_military}", "24 hour timestamp")
        ]

        embed.add_field(
            name="Date & Time Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in datetime_vars]),
            inline=False
        )

        # Boost variables
        boost_vars = [
            ("{boost.count}", "Amount of times user has boosted"),
            ("{guild.boost_count}", "Overall guild boost count")
        ]

        embed.add_field(
            name="Boost Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in boost_vars]),
            inline=False
        )

        # Button variables
        button_vars = [
            ("{button.invite}", "Bot invite link"),
            ("{button.support}", "Support server link"),
            ("{button.website}", "Website link"),
            ("{button.rules}", "Guild rules link"),
            ("{button.chat}", "Guild chat link")
        ]

        embed.add_field(
            name="Button Variables",
            value="\n".join([f"`{var}` - {desc}" for var, desc in button_vars]),
            inline=False
        )

        # Add button syntax info
        embed.add_field(
            name="Button Syntax",
            value="Use `$v{button: Label && URL}` to create clickable buttons\n"
                  "Example: `$v{button: Join Chat && {button.chat}}`",
            inline=False
        )

        embed.set_footer(text="Use these variables in your welcome messages to display dynamic content")

        await ctx.send(embed=embed)

    @greet.command(name="test", aliases=["wt"])
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def greet_test(self, ctx, *, script):
        """
        Usage: {0}greet test <script>
        Alias: {0}wt <script>
        Permission: Manage Guild
        Output:
            Tests a welcome script and shows how it will look
        """
        try:
            # Parse the script using the author as test member
            content, embed, view = await self.parse_message(script, ctx.author, ctx.channel)

            # Send simple test result
            test_embed = discord.Embed(
                title="Welcome Script Test",
                description="Here's how your welcome message will look:",
                color=0x323339
            )

            await ctx.send(embed=test_embed)

            # Send the actual preview
            if embed and view:
                await ctx.send(content=content, embed=embed, view=view)
            elif embed:
                await ctx.send(content=content, embed=embed)
            elif view:
                await ctx.send(content=content, view=view)
            elif content:
                await ctx.send(content)
            else:
                await ctx.send("*No content generated*")

        except Exception as e:
            # Show simple error
            error_embed = discord.Embed(
                title="Welcome Script Test Failed",
                description=f"Error: {str(e)}",
                color=0xff0000
            )
            await ctx.send(embed=error_embed)

    ######################
    ## Goodbye Commands ##
    ######################

    @decorators.group(
        invoke_without_command=True,
        brief="Set up goodbye messages in channels.",
        implemented="2024-01-01 00:00:00.000000",
        updated="2024-01-01 00:00:00.000000",
    )
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def goodbye(self, ctx):
        """
        Usage: {0}goodbye
        Permission: Manage Guild
        Output:
            Shows goodbye command help
        """
        await ctx.send_help(ctx.command)

    @goodbye.command(name="add")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def goodbye_add(self, ctx, channel: converters.DiscordChannel, *, message):
        """
        Usage: {0}goodbye add <channel> <message>
        Permission: Manage Guild
        Output:
            Adds a goodbye message for the specified channel
        """
        if not isinstance(channel, (discord.TextChannel, discord.Thread)):
            return await ctx.fail("Channel must be a text channel.")

        # Insert or update goodbye message
        query = """
                INSERT INTO goodbye_messages (server_id, channel_id, message)
                VALUES ($1, $2, $3)
                ON CONFLICT (server_id, channel_id)
                DO UPDATE SET message = $3, created_at = NOW();
                """
        await self.bot.cxn.execute(query, ctx.guild.id, channel.id, message)

        await ctx.success(f"Goodbye message set for {channel.mention}")

    @goodbye.command(name="remove")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def goodbye_remove(self, ctx, channel: converters.DiscordChannel):
        """
        Usage: {0}goodbye remove <channel>
        Permission: Manage Guild
        Output:
            Removes the goodbye message from the specified channel
        """
        query = """
                DELETE FROM goodbye_messages
                WHERE server_id = $1 AND channel_id = $2;
                """
        result = await self.bot.cxn.execute(query, ctx.guild.id, channel.id)

        if result == "DELETE 0":
            return await ctx.fail(f"No goodbye message found for {channel.mention}")

        await ctx.success(f"Goodbye message removed from {channel.mention}")

    @goodbye.command(name="view")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def goodbye_view(self, ctx, channel: converters.DiscordChannel):
        """
        Usage: {0}goodbye view <channel>
        Permission: Manage Guild
        Output:
            Shows the goodbye message for the specified channel
        """
        query = """
                SELECT message
                FROM goodbye_messages
                WHERE server_id = $1 AND channel_id = $2;
                """
        message = await self.bot.cxn.fetchval(query, ctx.guild.id, channel.id)

        if not message:
            return await ctx.fail(f"No goodbye message found for {channel.mention}")

        embed = discord.Embed(
            title=f"Goodbye Message for {channel.name}",
            description=f"```\n{message}\n```",
            color=0x323339
        )

        await ctx.send(embed=embed)

    @goodbye.command(name="list")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def goodbye_list(self, ctx):
        """
        Usage: {0}goodbye list
        Permission: Manage Guild
        Output:
            Shows all goodbye messages in the server
        """
        query = """
                SELECT channel_id, message
                FROM goodbye_messages
                WHERE server_id = $1
                ORDER BY created_at;
                """
        records = await self.bot.cxn.fetch(query, ctx.guild.id)

        if not records:
            return await ctx.fail("No goodbye messages found in this server.")

        embed = discord.Embed(
            title="Goodbye Messages",
            color=0x323339
        )

        for record in records:
            channel = self.bot.get_channel(record['channel_id'])
            channel_name = channel.mention if channel else f"<#{record['channel_id']}>"
            message_preview = record['message'][:100] + "..." if len(record['message']) > 100 else record['message']
            embed.add_field(
                name=channel_name,
                value=f"```\n{message_preview}\n```",
                inline=False
            )

        await ctx.send(embed=embed)

    @goodbye.command(name="variables")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.bot_has_perms(embed_links=True)
    async def goodbye_variables(self, ctx):
        """
        Usage: {0}goodbye variables
        Permission: Manage Guild
        Output:
            Shows all available variables for goodbye messages
        """
        # Reuse the same variables display as welcome
        await self.greet_variables(ctx)
